#!/usr/bin/env python3
"""
🔍 家庭私人医生小帮手 - 综合系统测试
全面测试前端、后端、MCP服务、AI模型的集成状态
"""

import requests
import json
import time
import subprocess
import logging
from pathlib import Path
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SystemIntegrationTester:
    """系统集成测试器"""
    
    def __init__(self):
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed_tests': 0,
                'failed_tests': 0,
                'success_rate': 0.0
            }
        }
        
        self.services = {
            'frontend': {'port': 3000, 'url': 'http://localhost:3000'},
            'backend': {'port': 5000, 'url': 'http://localhost:5000'},
            'mcp': {'port': 8006, 'url': 'http://localhost:8006'},
            'ollama': {'port': 11434, 'url': 'http://localhost:11434'}
        }
    
    def run_test(self, test_name, test_func):
        """运行单个测试"""
        logger.info(f"🧪 测试: {test_name}")
        self.test_results['summary']['total_tests'] += 1
        
        try:
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            if result:
                logger.info(f"✅ {test_name} - 通过 ({end_time-start_time:.2f}s)")
                self.test_results['tests'][test_name] = {
                    'status': 'PASS',
                    'duration': end_time - start_time,
                    'timestamp': datetime.now().isoformat()
                }
                self.test_results['summary']['passed_tests'] += 1
                return True
            else:
                logger.error(f"❌ {test_name} - 失败")
                self.test_results['tests'][test_name] = {
                    'status': 'FAIL',
                    'duration': end_time - start_time,
                    'timestamp': datetime.now().isoformat()
                }
                self.test_results['summary']['failed_tests'] += 1
                return False
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            self.test_results['tests'][test_name] = {
                'status': 'ERROR',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            self.test_results['summary']['failed_tests'] += 1
            return False
    
    def test_service_availability(self):
        """测试所有服务可用性"""
        all_services_up = True
        
        for service_name, service_info in self.services.items():
            try:
                if service_name == 'frontend':
                    response = requests.get(service_info['url'], timeout=5)
                    available = response.status_code == 200
                elif service_name == 'backend':
                    response = requests.get(f"{service_info['url']}/api/health", timeout=5)
                    available = response.status_code == 200
                elif service_name == 'mcp':
                    response = requests.get(f"{service_info['url']}/health", timeout=5)
                    available = response.status_code == 200
                elif service_name == 'ollama':
                    response = requests.get(f"{service_info['url']}/api/tags", timeout=5)
                    available = response.status_code == 200
                
                if available:
                    logger.info(f"✅ {service_name} 服务正常")
                else:
                    logger.warning(f"⚠️ {service_name} 服务异常")
                    all_services_up = False
                    
            except requests.exceptions.RequestException:
                logger.warning(f"⚠️ {service_name} 服务无法访问")
                all_services_up = False
        
        return all_services_up
    
    def test_deepseek_model(self):
        """测试DeepSeek本地模型"""
        try:
            # 检查Ollama服务
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code != 200:
                return False
            
            models = response.json().get('models', [])
            deepseek_models = [m for m in models if 'deepseek' in m['name'].lower()]
            
            if not deepseek_models:
                logger.warning("⚠️ 未找到DeepSeek模型")
                return False
            
            model_name = deepseek_models[0]['name']
            logger.info(f"✅ 使用模型: {model_name}")
            
            # 测试模型响应
            request_data = {
                "model": model_name,
                "prompt": "你是专业中医助手。问题：什么是中医？\n回答：",
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "num_predict": 100,
                    "num_ctx": 1024
                }
            }
            
            response = requests.post(
                "http://localhost:11434/api/generate",
                json=request_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                generated_text = result.get('response', '').strip()
                
                if generated_text and len(generated_text) > 20:
                    logger.info(f"✅ 模型响应正常，长度: {len(generated_text)}")
                    return True
                else:
                    logger.warning("⚠️ 模型响应为空或过短")
                    return False
            else:
                logger.warning(f"⚠️ 模型API异常: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ DeepSeek模型测试失败: {e}")
            return False
    
    def test_vector_database(self):
        """测试向量数据库"""
        try:
            response = requests.get("http://localhost:5000/api/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                vector_chunks = health_data.get('vector_db_chunks', 0)
                
                if vector_chunks >= 290:
                    logger.info(f"✅ 向量数据库完整: {vector_chunks}块")
                    return True
                else:
                    logger.warning(f"⚠️ 向量数据库不完整: {vector_chunks}块")
                    return False
            else:
                return False
        except Exception as e:
            logger.error(f"❌ 向量数据库测试失败: {e}")
            return False
    
    def test_embedding_model(self):
        """测试m3e-base嵌入模型"""
        model_path = Path("models/m3e-base")
        if model_path.exists():
            model_files = list(model_path.rglob("*"))
            if len(model_files) > 10:  # 模型文件应该有多个
                logger.info(f"✅ m3e-base模型完整: {len(model_files)}个文件")
                return True
            else:
                logger.warning(f"⚠️ m3e-base模型不完整: {len(model_files)}个文件")
                return False
        else:
            logger.warning("⚠️ m3e-base模型不存在")
            return False
    
    def test_end_to_end_chat(self):
        """测试端到端聊天功能"""
        try:
            test_query = "请简单介绍一下中医的基本理论"
            
            chat_data = {
                'message': test_query,
                'session_id': None
            }
            
            response = requests.post(
                'http://localhost:5000/api/chat',
                json=chat_data,
                timeout=45
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '')
                
                if ai_response and len(ai_response) > 50:
                    logger.info(f"✅ 端到端聊天成功，回答长度: {len(ai_response)}")
                    
                    # 检查是否包含中医相关内容
                    if any(keyword in ai_response for keyword in ['中医', '阴阳', '五行', '气血', '脏腑']):
                        logger.info("✅ 回答内容相关")
                        return True
                    else:
                        logger.warning("⚠️ 回答内容可能不够相关")
                        return False
                else:
                    logger.warning("⚠️ AI回答为空或过短")
                    return False
            else:
                logger.warning(f"⚠️ 聊天API异常: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ 端到端聊天测试失败: {e}")
            return False
    
    def test_frontend_backend_integration(self):
        """测试前后端集成"""
        try:
            # 测试前端是否能访问
            frontend_response = requests.get("http://localhost:3000", timeout=5)
            if frontend_response.status_code != 200:
                logger.warning("⚠️ 前端服务异常")
                return False
            
            # 测试后端API
            backend_response = requests.get("http://localhost:5000/api/health", timeout=5)
            if backend_response.status_code != 200:
                logger.warning("⚠️ 后端服务异常")
                return False
            
            logger.info("✅ 前后端服务正常")
            return True
            
        except Exception as e:
            logger.error(f"❌ 前后端集成测试失败: {e}")
            return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("🚀 开始综合系统测试...")
        logger.info("=" * 60)
        
        # 定义测试列表
        tests = [
            ("服务可用性检查", self.test_service_availability),
            ("DeepSeek本地模型", self.test_deepseek_model),
            ("向量数据库状态", self.test_vector_database),
            ("m3e-base嵌入模型", self.test_embedding_model),
            ("前后端集成", self.test_frontend_backend_integration),
            ("端到端聊天功能", self.test_end_to_end_chat)
        ]
        
        # 运行所有测试
        for test_name, test_func in tests:
            self.run_test(test_name, test_func)
            time.sleep(2)  # 测试间隔
        
        # 计算成功率
        total = self.test_results['summary']['total_tests']
        passed = self.test_results['summary']['passed_tests']
        self.test_results['summary']['success_rate'] = (passed / total * 100) if total > 0 else 0
        
        # 输出结果
        self.print_summary()
        self.save_results()
    
    def print_summary(self):
        """打印测试摘要"""
        summary = self.test_results['summary']
        
        logger.info("=" * 60)
        logger.info("📊 综合系统测试结果")
        logger.info("=" * 60)
        logger.info(f"总测试数: {summary['total_tests']}")
        logger.info(f"通过测试: {summary['passed_tests']}")
        logger.info(f"失败测试: {summary['failed_tests']}")
        logger.info(f"成功率: {summary['success_rate']:.1f}%")
        
        if summary['success_rate'] >= 90:
            logger.info("🎉 系统运行状态优秀！")
        elif summary['success_rate'] >= 70:
            logger.info("✅ 系统运行状态良好")
        elif summary['success_rate'] >= 50:
            logger.warning("⚠️ 系统部分功能异常")
        else:
            logger.error("❌ 系统存在严重问题")
        
        logger.info("=" * 60)
    
    def save_results(self):
        """保存测试结果"""
        try:
            with open('comprehensive_test_report.json', 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False)
            
            logger.info("📄 测试报告已保存: comprehensive_test_report.json")
            
        except Exception as e:
            logger.error(f"❌ 保存测试报告失败: {e}")

def main():
    """主函数"""
    logger.info("🏥 家庭私人医生小帮手 - 综合系统测试")
    
    tester = SystemIntegrationTester()
    tester.run_comprehensive_test()

if __name__ == '__main__':
    main()
