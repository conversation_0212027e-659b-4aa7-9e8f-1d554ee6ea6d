#!/usr/bin/env python3
"""
调试后端启动问题
"""

import sys
import traceback
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """测试所有导入"""
    logger.info("🔍 测试导入...")
    
    try:
        from fastapi import FastAPI, HTTPException
        logger.info("✅ FastAPI导入成功")
    except Exception as e:
        logger.error(f"❌ FastAPI导入失败: {e}")
        return False
    
    try:
        from intelligent_rag_retriever import IntelligentRAGRetriever
        logger.info("✅ RAG检索器导入成功")
    except Exception as e:
        logger.error(f"❌ RAG检索器导入失败: {e}")
    
    try:
        from deepseek_ollama_api import DeepSeekOllamaAPI
        logger.info("✅ DeepSeek API导入成功")
    except Exception as e:
        logger.error(f"❌ DeepSeek API导入失败: {e}")
    
    try:
        from intelligent_mcp_service import IntelligentSearchEngine
        logger.info("✅ MCP服务导入成功")
    except Exception as e:
        logger.error(f"❌ MCP服务导入失败: {e}")
    
    return True

def test_rag_initialization():
    """测试RAG初始化"""
    logger.info("🔍 测试RAG初始化...")
    
    try:
        from intelligent_rag_retriever import IntelligentRAGRetriever
        rag = IntelligentRAGRetriever()
        logger.info("✅ RAG实例创建成功")
        
        if rag.initialize():
            logger.info("✅ RAG初始化成功")
            if hasattr(rag, 'chunks'):
                logger.info(f"📊 向量数据库: {len(rag.chunks)} 个文档块")
            return True
        else:
            logger.error("❌ RAG初始化失败")
            return False
    except Exception as e:
        logger.error(f"❌ RAG初始化异常: {e}")
        traceback.print_exc()
        return False

def test_deepseek_initialization():
    """测试DeepSeek初始化"""
    logger.info("🔍 测试DeepSeek初始化...")
    
    try:
        from deepseek_ollama_api import DeepSeekOllamaAPI
        deepseek = DeepSeekOllamaAPI()
        logger.info("✅ DeepSeek实例创建成功")
        
        if deepseek.available:
            logger.info("✅ DeepSeek模型可用")
            if hasattr(deepseek, 'model_name'):
                logger.info(f"🤖 模型名称: {deepseek.model_name}")
            return True
        else:
            logger.error("❌ DeepSeek模型不可用")
            return False
    except Exception as e:
        logger.error(f"❌ DeepSeek初始化异常: {e}")
        traceback.print_exc()
        return False

def test_fastapi_creation():
    """测试FastAPI应用创建"""
    logger.info("🔍 测试FastAPI应用创建...")
    
    try:
        from fastapi import FastAPI
        from fastapi.middleware.cors import CORSMiddleware
        
        app = FastAPI(title="测试应用")
        logger.info("✅ FastAPI应用创建成功")
        
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        logger.info("✅ CORS中间件添加成功")
        
        @app.get("/test")
        async def test_endpoint():
            return {"status": "ok"}
        
        logger.info("✅ 测试端点添加成功")
        return app
    except Exception as e:
        logger.error(f"❌ FastAPI应用创建失败: {e}")
        traceback.print_exc()
        return None

def test_uvicorn_startup():
    """测试Uvicorn启动"""
    logger.info("🔍 测试Uvicorn启动...")
    
    try:
        import uvicorn
        logger.info("✅ Uvicorn导入成功")
        
        app = test_fastapi_creation()
        if not app:
            return False
        
        # 测试配置
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=5001,
            log_level="info"
        )
        logger.info("✅ Uvicorn配置创建成功")
        
        return True
    except Exception as e:
        logger.error(f"❌ Uvicorn测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("🚀 开始后端启动诊断...")
    
    # 测试导入
    if not test_imports():
        logger.error("❌ 导入测试失败，无法继续")
        return False
    
    # 测试RAG初始化
    rag_ok = test_rag_initialization()
    
    # 测试DeepSeek初始化
    deepseek_ok = test_deepseek_initialization()
    
    # 测试FastAPI创建
    fastapi_ok = test_fastapi_creation() is not None
    
    # 测试Uvicorn启动
    uvicorn_ok = test_uvicorn_startup()
    
    # 总结
    logger.info("=" * 50)
    logger.info("📊 诊断结果:")
    logger.info(f"   RAG检索器: {'✅' if rag_ok else '❌'}")
    logger.info(f"   DeepSeek模型: {'✅' if deepseek_ok else '❌'}")
    logger.info(f"   FastAPI应用: {'✅' if fastapi_ok else '❌'}")
    logger.info(f"   Uvicorn服务: {'✅' if uvicorn_ok else '❌'}")
    
    if all([rag_ok, deepseek_ok, fastapi_ok, uvicorn_ok]):
        logger.info("🎉 所有组件测试通过，后端应该可以正常启动")
        return True
    else:
        logger.error("❌ 存在组件问题，需要修复")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"❌ 诊断过程异常: {e}")
        traceback.print_exc()
        sys.exit(1)
