#!/usr/bin/env python3
"""
🧪 FastAPI迁移验证测试
验证Flask到FastAPI的完整迁移是否成功
"""

import requests
import json
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FastAPIMigrationTester:
    """FastAPI迁移测试器"""
    
    def __init__(self):
        self.base_url = "http://localhost:5001"
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed_tests': 0,
                'failed_tests': 0,
                'success_rate': 0.0
            }
        }
    
    def run_test(self, test_name, test_func):
        """运行单个测试"""
        logger.info(f"🧪 测试: {test_name}")
        self.test_results['summary']['total_tests'] += 1
        
        try:
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            if result:
                logger.info(f"✅ {test_name} - 通过 ({end_time-start_time:.2f}s)")
                self.test_results['tests'][test_name] = {
                    'status': 'PASS',
                    'duration': end_time - start_time
                }
                self.test_results['summary']['passed_tests'] += 1
                return True
            else:
                logger.error(f"❌ {test_name} - 失败")
                self.test_results['tests'][test_name] = {
                    'status': 'FAIL',
                    'duration': end_time - start_time
                }
                self.test_results['summary']['failed_tests'] += 1
                return False
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            self.test_results['tests'][test_name] = {
                'status': 'ERROR',
                'error': str(e)
            }
            self.test_results['summary']['failed_tests'] += 1
            return False
    
    def test_health_endpoint(self):
        """测试健康检查端点"""
        try:
            response = requests.get(f"{self.base_url}/api/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                # 验证响应结构
                required_fields = ['status', 'timestamp', 'components', 'vector_db_chunks', 'service', 'version']
                for field in required_fields:
                    if field not in data:
                        logger.error(f"缺少字段: {field}")
                        return False
                
                # 验证版本信息
                if 'fastapi' not in data['version'].lower():
                    logger.error(f"版本信息不正确: {data['version']}")
                    return False
                
                logger.info(f"✅ 健康检查成功: {data['status']}")
                logger.info(f"📊 向量数据库: {data['vector_db_chunks']}块")
                logger.info(f"🔧 版本: {data['version']}")
                return True
            else:
                logger.error(f"健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            return False
    
    def test_chat_endpoint(self):
        """测试聊天端点"""
        try:
            chat_data = {
                'message': '请简单介绍一下中医的基本理论',
                'session_id': None
            }
            
            response = requests.post(
                f"{self.base_url}/api/chat",
                json=chat_data,
                timeout=45
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # 验证响应结构
                required_fields = ['response', 'session_id', 'response_time', 'timestamp']
                for field in required_fields:
                    if field not in data:
                        logger.error(f"聊天响应缺少字段: {field}")
                        return False
                
                # 验证响应内容
                if not data['response'] or len(data['response']) < 50:
                    logger.error("AI回答为空或过短")
                    return False
                
                # 检查是否包含中医相关内容
                if any(keyword in data['response'] for keyword in ['中医', '阴阳', '五行', '气血', '脏腑']):
                    logger.info(f"✅ 聊天成功，响应时间: {data['response_time']:.2f}秒")
                    logger.info(f"📝 回答长度: {len(data['response'])}字符")
                    return True
                else:
                    logger.warning("回答内容可能不够相关")
                    return False
            else:
                logger.error(f"聊天请求失败: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"聊天测试异常: {e}")
            return False
    
    def test_sessions_endpoint(self):
        """测试会话管理端点"""
        try:
            # 测试获取会话列表
            response = requests.get(f"{self.base_url}/api/sessions", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if 'sessions' in data:
                    logger.info(f"✅ 会话列表获取成功: {len(data['sessions'])}个会话")
                    return True
                else:
                    logger.error("会话列表响应格式错误")
                    return False
            else:
                logger.error(f"获取会话列表失败: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"会话管理测试异常: {e}")
            return False
    
    def test_documents_endpoint(self):
        """测试文档管理端点"""
        try:
            response = requests.get(f"{self.base_url}/api/documents", timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                # 验证响应结构
                required_fields = ['documents', 'total_count', 'vector_db_chunks']
                for field in required_fields:
                    if field not in data:
                        logger.error(f"文档响应缺少字段: {field}")
                        return False
                
                logger.info(f"✅ 文档管理成功: {data['total_count']}个文档")
                logger.info(f"📊 向量块数: {data['vector_db_chunks']}")
                return True
            else:
                logger.error(f"文档管理请求失败: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"文档管理测试异常: {e}")
            return False
    
    def test_api_compatibility(self):
        """测试API兼容性"""
        try:
            # 测试所有主要端点是否可访问
            endpoints = [
                '/api/health',
                '/api/sessions',
                '/api/documents'
            ]
            
            for endpoint in endpoints:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                if response.status_code not in [200, 201]:
                    logger.error(f"端点 {endpoint} 不可访问: {response.status_code}")
                    return False
            
            logger.info("✅ API兼容性测试通过")
            return True
        except Exception as e:
            logger.error(f"API兼容性测试异常: {e}")
            return False
    
    def test_performance(self):
        """测试性能"""
        try:
            # 测试健康检查响应时间
            start_time = time.time()
            response = requests.get(f"{self.base_url}/api/health", timeout=5)
            end_time = time.time()
            
            if response.status_code == 200:
                response_time = end_time - start_time
                if response_time < 3.0:  # 3秒内响应
                    logger.info(f"✅ 性能测试通过: {response_time:.2f}秒")
                    return True
                else:
                    logger.warning(f"⚠️ 响应时间较慢: {response_time:.2f}秒")
                    return False
            else:
                logger.error(f"性能测试失败: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"性能测试异常: {e}")
            return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("🚀 开始FastAPI迁移验证测试...")
        logger.info("=" * 60)
        
        # 定义测试列表
        tests = [
            ("健康检查端点", self.test_health_endpoint),
            ("聊天功能", self.test_chat_endpoint),
            ("会话管理", self.test_sessions_endpoint),
            ("文档管理", self.test_documents_endpoint),
            ("API兼容性", self.test_api_compatibility),
            ("性能测试", self.test_performance)
        ]
        
        # 运行所有测试
        for test_name, test_func in tests:
            self.run_test(test_name, test_func)
            time.sleep(1)  # 测试间隔
        
        # 计算成功率
        total = self.test_results['summary']['total_tests']
        passed = self.test_results['summary']['passed_tests']
        self.test_results['summary']['success_rate'] = (passed / total * 100) if total > 0 else 0
        
        # 输出结果
        self.print_summary()
    
    def print_summary(self):
        """打印测试摘要"""
        summary = self.test_results['summary']
        
        logger.info("=" * 60)
        logger.info("📊 FastAPI迁移验证结果")
        logger.info("=" * 60)
        logger.info(f"总测试数: {summary['total_tests']}")
        logger.info(f"通过测试: {summary['passed_tests']}")
        logger.info(f"失败测试: {summary['failed_tests']}")
        logger.info(f"成功率: {summary['success_rate']:.1f}%")
        
        if summary['success_rate'] >= 90:
            logger.info("🎉 FastAPI迁移成功！")
        elif summary['success_rate'] >= 70:
            logger.info("✅ FastAPI迁移基本成功")
        else:
            logger.error("❌ FastAPI迁移存在问题")
        
        logger.info("=" * 60)

def main():
    """主函数"""
    logger.info("🏥 家庭私人医生小帮手 - FastAPI迁移验证")
    
    tester = FastAPIMigrationTester()
    tester.run_comprehensive_test()

if __name__ == '__main__':
    main()
