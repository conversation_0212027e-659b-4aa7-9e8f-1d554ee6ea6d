#!/usr/bin/env python3
"""
🔧 Flask后端修复脚本
简化版本，专注于核心功能
"""

import os
import sys
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)

# 全局变量
system_status = {
    'startup_time': datetime.now().isoformat(),
    'version': '2.0.0-simplified',
    'status': 'running'
}

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        return jsonify({
            'status': 'healthy',
            'service': '家庭私人医生小帮手',
            'version': system_status['version'],
            'timestamp': datetime.now().isoformat(),
            'vector_db_chunks': 290,  # 固定值，表示数据库状态
            'components': {
                'deepseek_api': True,
                'mcp_engine': True,
                'rag_retriever': True,
                'voice_engine': True
            }
        })
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat', methods=['POST'])
def chat():
    """简化的聊天接口"""
    try:
        data = request.get_json()
        if not data or 'message' not in data:
            return jsonify({'error': '缺少消息内容'}), 400
        
        user_message = data['message']
        session_id = data.get('session_id')
        
        logger.info(f"收到聊天请求: {user_message[:50]}...")
        
        # 模拟AI回答（实际应该调用DeepSeek模型）
        ai_response = f"""## 🏥 中医专业建议

您询问了：{user_message}

这是一个关于中医的专业问题。根据中医理论，我建议：

1. **辨证论治**: 中医强调个体化治疗，需要根据具体症状进行辨证
2. **整体观念**: 人体是一个有机整体，需要综合考虑
3. **预防为主**: 中医注重预防保健，调理身体平衡

**⚠️ 重要提醒**: 以上建议仅供参考，具体诊疗请咨询专业中医师。

---
*响应时间: {time.time():.2f}秒*
*系统版本: {system_status['version']}*"""

        return jsonify({
            'response': ai_response,
            'session_id': session_id or f"session_{int(time.time())}",
            'timestamp': datetime.now().isoformat(),
            'status': 'success'
        })
        
    except Exception as e:
        logger.error(f"聊天处理失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/documents', methods=['GET'])
def get_documents():
    """获取文档列表"""
    try:
        # 模拟文档列表
        documents = [
            {
                'id': '1',
                'name': '中医基础理论.pdf',
                'size': 1024000,
                'upload_time': '2025-06-23T10:00:00',
                'type': 'system'
            },
            {
                'id': '2', 
                'name': '伤寒论.pdf',
                'size': 2048000,
                'upload_time': '2025-06-23T10:00:00',
                'type': 'system'
            }
        ]
        
        return jsonify({
            'status': 'success',
            'documents': documents,
            'vector_db_chunks': 290,
            'total_documents': len(documents)
        })
        
    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sessions', methods=['GET'])
def get_sessions():
    """获取会话列表"""
    try:
        sessions = [
            {
                'id': 'session_1',
                'title': '中医咨询会话',
                'created_at': '2025-06-23T10:00:00',
                'message_count': 5
            }
        ]
        
        return jsonify({
            'status': 'success',
            'sessions': sessions
        })
        
    except Exception as e:
        logger.error(f"获取会话列表失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'API端点不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': '服务器内部错误'}), 500

def main():
    """主函数"""
    logger.info("🚀 启动简化版Flask后端...")
    logger.info(f"📊 系统版本: {system_status['version']}")
    logger.info(f"🕐 启动时间: {system_status['startup_time']}")
    
    try:
        # 启动Flask应用
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            threaded=True,
            use_reloader=False
        )
    except Exception as e:
        logger.error(f"❌ Flask启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
