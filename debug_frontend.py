#!/usr/bin/env python3
"""
前端界面问题诊断工具
用于检查Vue.js前端的加载状态和可能的问题
"""

import requests
import json
import time
import subprocess
import sys
from pathlib import Path

def check_frontend_status():
    """检查前端服务状态"""
    print("🔍 检查前端服务状态...")
    
    try:
        response = requests.get('http://localhost:3000', timeout=10)
        print(f"✅ 前端服务响应: {response.status_code}")
        
        # 检查响应内容
        content = response.text
        if 'id="app"' in content:
            print("✅ Vue.js应用容器存在")
        else:
            print("❌ Vue.js应用容器缺失")
            
        if 'bootstrap' in content.lower():
            print("✅ Bootstrap样式已加载")
        else:
            print("❌ Bootstrap样式可能缺失")
            
        return True
    except Exception as e:
        print(f"❌ 前端服务检查失败: {e}")
        return False

def check_backend_status():
    """检查后端服务状态"""
    print("\n🔍 检查后端服务状态...")
    
    try:
        response = requests.get('http://localhost:5001/api/health', timeout=10)
        health_data = response.json()
        print(f"✅ 后端健康状态: {health_data['status']}")
        print(f"   组件状态: {health_data['components']}")
        print(f"   向量数据库: {health_data['vector_db_chunks']} 个文档块")
        return True
    except Exception as e:
        print(f"❌ 后端服务检查失败: {e}")
        return False

def check_api_endpoints():
    """检查关键API端点"""
    print("\n🔍 检查关键API端点...")
    
    endpoints = [
        '/api/health',
        '/api/chat',
        '/api/sessions',
        '/api/documents'
    ]
    
    for endpoint in endpoints:
        try:
            if endpoint == '/api/chat':
                # POST请求测试
                response = requests.post(f'http://localhost:5001{endpoint}', 
                                       json={'message': 'test'}, 
                                       timeout=5)
            else:
                # GET请求测试
                response = requests.get(f'http://localhost:5001{endpoint}', timeout=5)
            
            print(f"✅ {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")

def check_vue_build():
    """检查Vue.js构建状态"""
    print("\n🔍 检查Vue.js构建状态...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    
    # 检查关键文件
    key_files = [
        "package.json",
        "src/main.js",
        "src/App.vue",
        "src/views/ChatView.vue",
        "src/router/index.js",
        "src/services/api.js"
    ]
    
    for file_path in key_files:
        full_path = frontend_dir / file_path
        if full_path.exists():
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 缺失")
    
    return True

def test_chat_functionality():
    """测试聊天功能"""
    print("\n🔍 测试聊天功能...")
    
    try:
        # 测试简单聊天
        response = requests.post('http://localhost:5001/api/chat', 
                               json={'message': '你好'}, 
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 聊天功能正常")
            print(f"   响应时间: {data.get('response_time', 'N/A')}秒")
            print(f"   会话ID: {data.get('session_id', 'N/A')}")
            print(f"   AI回复长度: {len(data.get('response', ''))}")
            return True
        else:
            print(f"❌ 聊天功能异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 聊天功能测试失败: {e}")
        return False

def check_browser_console():
    """检查浏览器控制台错误（模拟）"""
    print("\n🔍 检查可能的前端错误...")
    
    # 检查常见的前端问题
    issues = []
    
    # 检查API配置
    try:
        with open("frontend/src/services/api.js", "r", encoding="utf-8") as f:
            api_content = f.read()
            if "localhost:5001" in api_content:
                print("✅ API端点配置正确")
            else:
                issues.append("API端点配置可能错误")
    except Exception as e:
        issues.append(f"无法读取API配置: {e}")
    
    # 检查路由配置
    try:
        with open("frontend/src/router/index.js", "r", encoding="utf-8") as f:
            router_content = f.read()
            if "ChatView" in router_content:
                print("✅ 路由配置包含ChatView")
            else:
                issues.append("路由配置缺少ChatView")
    except Exception as e:
        issues.append(f"无法读取路由配置: {e}")
    
    if issues:
        print("❌ 发现潜在问题:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("✅ 未发现明显的配置问题")
    
    return len(issues) == 0

def main():
    """主诊断流程"""
    print("🏥 TCM前端界面问题诊断工具")
    print("=" * 50)
    
    results = {
        'frontend_status': False,
        'backend_status': False,
        'vue_build': False,
        'chat_functionality': False,
        'config_check': False
    }
    
    # 执行各项检查
    results['frontend_status'] = check_frontend_status()
    results['backend_status'] = check_backend_status()
    check_api_endpoints()
    results['vue_build'] = check_vue_build()
    results['chat_functionality'] = test_chat_functionality()
    results['config_check'] = check_browser_console()
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 诊断结果汇总:")
    
    all_passed = True
    for check, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {check}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查通过！前端应该正常工作。")
        print("💡 如果界面仍然空白，请检查浏览器控制台错误。")
    else:
        print("⚠️  发现问题，需要进一步修复。")
        print("💡 建议按照失败的检查项进行逐一修复。")
    
    return all_passed

if __name__ == "__main__":
    main()
