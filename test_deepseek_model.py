#!/usr/bin/env python3
"""
测试DeepSeek本地模型
"""

import requests
import json
import time

def test_deepseek_model():
    """测试DeepSeek模型"""
    print("🧪 测试DeepSeek本地模型...")
    
    # 测试Ollama服务
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama服务正常，可用模型数: {len(models)}")
            
            # 查找DeepSeek模型
            deepseek_models = [m for m in models if 'deepseek' in m['name'].lower()]
            if deepseek_models:
                model_name = deepseek_models[0]['name']
                print(f"✅ 找到DeepSeek模型: {model_name}")
                
                # 测试模型响应
                test_query = "请简单介绍一下栀子甘草豉汤方"
                
                request_data = {
                    "model": model_name,
                    "prompt": f"你是专业中医助手。问题：{test_query}\n回答：",
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "num_predict": 200,
                        "top_p": 0.9,
                        "num_ctx": 2048
                    }
                }
                
                print(f"🔍 测试查询: {test_query}")
                start_time = time.time()
                
                response = requests.post(
                    "http://localhost:11434/api/generate",
                    json=request_data,
                    timeout=60
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status_code == 200:
                    result = response.json()
                    generated_text = result.get('response', '').strip()
                    
                    print(f"✅ 模型响应成功")
                    print(f"⏱️ 响应时间: {response_time:.2f}秒")
                    print(f"📝 回答长度: {len(generated_text)}字符")
                    print(f"📄 回答内容: {generated_text[:200]}...")
                    
                    return True
                else:
                    print(f"❌ 模型响应失败: {response.status_code}")
                    return False
            else:
                print("❌ 未找到DeepSeek模型")
                return False
        else:
            print(f"❌ Ollama服务异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_deepseek_model()
