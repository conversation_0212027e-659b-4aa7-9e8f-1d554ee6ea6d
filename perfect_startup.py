#!/usr/bin/env python3
"""
🏥 家庭私人医生小帮手 - 统一启动脚本
Vue.js + FastAPI 架构的完整系统启动器

功能特性:
- 单文件启动前后端服务
- 自动检查依赖和环境
- 智能端口管理
- 系统健康监控
- 优雅关闭处理
- 错误恢复机制

作者: TCM RAG System Team
版本: 2.0.0
"""

import os
import sys
import time
import signal
import subprocess
import threading
import logging
import json
from pathlib import Path
from datetime import datetime
import webbrowser
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('perfect_system.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class SystemManager:
    """系统管理器 - 负责启动和管理所有服务"""
    
    def __init__(self):
        self.processes = {}
        self.is_running = False
        self.start_time = None
        
        # 服务配置
        self.services = {
            'fastapi_backend': {
                'name': 'FastAPI后端服务',
                'command': [sys.executable, 'fastapi_backend.py'],
                'port': 5001,
                'health_url': 'http://localhost:5001/api/health',
                'required': True,
                'startup_delay': 3
            },
            'mcp_service': {
                'name': 'MCP智能服务',
                'command': [sys.executable, 'intelligent_mcp_service.py'],
                'port': 8006,
                'health_url': 'http://localhost:8006/health',
                'required': True,
                'startup_delay': 2
            },
            'vue_frontend': {
                'name': 'Vue.js前端服务',
                'command': ['npm', 'run', 'serve'],
                'port': 3000,
                'health_url': 'http://localhost:3000',
                'required': True,
                'startup_delay': 5,
                'cwd': './frontend'
            }
        }
        
        # 系统路径
        self.base_path = Path.cwd()
        self.frontend_path = self.base_path / 'frontend'
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器 - 优雅关闭"""
        logger.info(f"收到信号 {signum}，开始优雅关闭...")
        self.shutdown()
        sys.exit(0)
    
    def check_dependencies(self) -> bool:
        """检查系统依赖"""
        logger.info("🔍 检查系统依赖...")
        
        # 检查Python依赖
        python_deps = [
            ('fastapi', 'fastapi'),
            ('uvicorn', 'uvicorn'),
            ('requests', 'requests'),
            ('pathlib', 'pathlib'),
            ('sentence-transformers', 'sentence_transformers'),
            ('faiss-cpu', 'faiss'),
            ('pyttsx3', 'pyttsx3'),
            ('speech-recognition', 'speech_recognition')
        ]

        missing_deps = []
        for dep_name, import_name in python_deps:
            try:
                __import__(import_name)
            except ImportError:
                missing_deps.append(dep_name)
        
        if missing_deps:
            logger.error(f"❌ 缺少Python依赖: {', '.join(missing_deps)}")
            logger.info("请运行: pip install -r requirements_perfect.txt")
            return False
        
        # 检查Node.js和npm
        try:
            node_result = subprocess.run(['node', '--version'], check=True, capture_output=True, text=True, shell=True)
            npm_result = subprocess.run(['npm', '--version'], check=True, capture_output=True, text=True, shell=True)
            logger.info(f"✅ Node.js版本: {node_result.stdout.strip()}")
            logger.info(f"✅ npm版本: {npm_result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("❌ 未找到Node.js或npm，请先安装Node.js")
            return False
        
        # 检查前端依赖
        if not (self.frontend_path / 'node_modules').exists():
            logger.info("📦 安装前端依赖...")
            try:
                subprocess.run(
                    ['npm', 'install'],
                    cwd=str(self.frontend_path),
                    check=True,
                    capture_output=True,
                    shell=True
                )
                logger.info("✅ 前端依赖安装完成")
            except subprocess.CalledProcessError as e:
                logger.error(f"❌ 前端依赖安装失败: {e}")
                return False
        
        # 检查必要文件
        required_files = [
            'fastapi_backend.py',
            'intelligent_mcp_service.py',
            'intelligent_rag_retriever.py',
            'deepseek_ollama_api.py',
            'frontend/package.json',
            'frontend/src/main.js'
        ]
        
        for file_path in required_files:
            if not (self.base_path / file_path).exists():
                logger.error(f"❌ 缺少必要文件: {file_path}")
                return False
        
        logger.info("✅ 依赖检查完成")
        return True
    
    def check_ports(self) -> bool:
        """检查端口可用性"""
        logger.info("🔌 检查端口可用性...")
        
        import socket
        
        for service_name, config in self.services.items():
            port = config['port']
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                result = sock.connect_ex(('localhost', port))
                if result == 0:
                    logger.warning(f"⚠️ 端口 {port} 已被占用 ({config['name']})")
                    # 尝试终止占用端口的进程
                    self._kill_port_process(port)
        
        logger.info("✅ 端口检查完成")
        return True
    
    def _kill_port_process(self, port: int):
        """终止占用指定端口的进程"""
        try:
            if os.name == 'nt':  # Windows
                result = subprocess.run(
                    ['netstat', '-ano'], 
                    capture_output=True, 
                    text=True
                )
                for line in result.stdout.split('\n'):
                    if f':{port}' in line and 'LISTENING' in line:
                        pid = line.strip().split()[-1]
                        subprocess.run(['taskkill', '/F', '/PID', pid], capture_output=True)
                        logger.info(f"已终止占用端口 {port} 的进程 (PID: {pid})")
            else:  # Unix/Linux/macOS
                result = subprocess.run(
                    ['lsof', '-ti', f':{port}'], 
                    capture_output=True, 
                    text=True
                )
                if result.stdout.strip():
                    pid = result.stdout.strip()
                    subprocess.run(['kill', '-9', pid], capture_output=True)
                    logger.info(f"已终止占用端口 {port} 的进程 (PID: {pid})")
        except Exception as e:
            logger.warning(f"无法终止端口 {port} 的进程: {e}")
    
    def start_service(self, service_name: str) -> bool:
        """启动单个服务"""
        config = self.services[service_name]
        logger.info(f"🚀 启动 {config['name']}...")
        
        try:
            # 设置工作目录
            cwd = str(self.base_path / config.get('cwd', '.'))

            # 启动进程
            process = subprocess.Popen(
                config['command'],
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                shell=True
            )
            
            self.processes[service_name] = {
                'process': process,
                'config': config,
                'start_time': time.time()
            }
            
            # 等待启动延迟
            time.sleep(config.get('startup_delay', 1))
            
            # 检查进程状态
            if process.poll() is not None:
                stdout, stderr = process.communicate()
                logger.error(f"❌ {config['name']} 启动失败")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return False
            
            logger.info(f"✅ {config['name']} 启动成功 (PID: {process.pid})")
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动 {config['name']} 时发生错误: {e}")
            return False
    
    def check_service_health(self, service_name: str) -> bool:
        """检查服务健康状态"""
        config = self.services[service_name]
        
        if 'health_url' not in config:
            return True
        
        try:
            import requests
            response = requests.get(config['health_url'], timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def start_all_services(self) -> bool:
        """启动所有服务"""
        logger.info("🎯 开始启动所有服务...")
        self.start_time = datetime.now()
        
        # 按顺序启动服务
        service_order = ['mcp_service', 'fastapi_backend', 'vue_frontend']
        
        for service_name in service_order:
            if not self.start_service(service_name):
                logger.error(f"❌ 服务启动失败: {service_name}")
                return False
            
            # 等待服务就绪
            logger.info(f"⏳ 等待 {self.services[service_name]['name']} 就绪...")
            max_wait = 30
            wait_time = 0
            
            while wait_time < max_wait:
                if self.check_service_health(service_name):
                    logger.info(f"✅ {self.services[service_name]['name']} 就绪")
                    break
                time.sleep(1)
                wait_time += 1
            else:
                logger.warning(f"⚠️ {self.services[service_name]['name']} 健康检查超时")
        
        self.is_running = True
        logger.info("🎉 所有服务启动完成！")
        return True
    
    def monitor_services(self):
        """监控服务状态"""
        logger.info("👁️ 开始监控服务状态...")
        
        while self.is_running:
            try:
                for service_name, service_info in self.processes.items():
                    process = service_info['process']
                    config = service_info['config']
                    
                    # 检查进程是否还在运行
                    if process.poll() is not None:
                        logger.error(f"❌ {config['name']} 进程已退出")
                        
                        # 如果是必需服务，尝试重启
                        if config.get('required', False):
                            logger.info(f"🔄 尝试重启 {config['name']}...")
                            if self.start_service(service_name):
                                logger.info(f"✅ {config['name']} 重启成功")
                            else:
                                logger.error(f"❌ {config['name']} 重启失败")
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"监控服务时发生错误: {e}")
                time.sleep(5)
    
    def show_system_info(self):
        """显示系统信息"""
        print("\n" + "="*60)
        print("🏥 家庭私人医生小帮手 v2.0.0")
        print("Vue.js + FastAPI 架构")
        print("="*60)
        print(f"启动时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"运行时长: {datetime.now() - self.start_time}")
        print("\n📊 服务状态:")
        
        for service_name, service_info in self.processes.items():
            config = service_info['config']
            process = service_info['process']
            status = "运行中" if process.poll() is None else "已停止"
            health = "健康" if self.check_service_health(service_name) else "异常"
            
            print(f"  • {config['name']}: {status} | {health} | 端口: {config['port']}")
        
        print("\n🌐 访问地址:")
        print("  • 前端界面: http://localhost:3000")
        print("  • 后端API: http://localhost:5001")
        print("  • MCP服务: http://localhost:8006")
        print("\n💡 使用说明:")
        print("  • 在浏览器中打开 http://localhost:3000 开始使用")
        print("  • 支持语音对话、文档上传、历史记录等功能")
        print("  • 按 Ctrl+C 优雅关闭系统")
        print("="*60)
    
    def open_browser(self):
        """打开浏览器"""
        try:
            time.sleep(2)  # 等待前端完全启动
            webbrowser.open('http://localhost:3000')
            logger.info("🌐 已打开浏览器")
        except Exception as e:
            logger.warning(f"无法自动打开浏览器: {e}")
    
    def shutdown(self):
        """关闭所有服务"""
        if not self.is_running:
            return
        
        logger.info("🛑 开始关闭系统...")
        self.is_running = False
        
        # 关闭所有进程
        for service_name, service_info in self.processes.items():
            process = service_info['process']
            config = service_info['config']
            
            logger.info(f"🔄 关闭 {config['name']}...")
            
            try:
                # 优雅关闭
                process.terminate()
                
                # 等待进程结束
                try:
                    process.wait(timeout=5)
                    logger.info(f"✅ {config['name']} 已关闭")
                except subprocess.TimeoutExpired:
                    # 强制关闭
                    process.kill()
                    process.wait()
                    logger.info(f"⚡ {config['name']} 已强制关闭")
                    
            except Exception as e:
                logger.error(f"关闭 {config['name']} 时发生错误: {e}")
        
        logger.info("✅ 系统关闭完成")
    
    def run(self):
        """运行系统"""
        try:
            # 显示启动信息
            print("🏥 家庭私人医生小帮手 v2.0.0")
            print("正在启动 Vue.js + FastAPI 架构系统...")
            print()
            
            # 检查依赖
            if not self.check_dependencies():
                return False
            
            # 检查端口
            if not self.check_ports():
                return False
            
            # 启动所有服务
            if not self.start_all_services():
                return False
            
            # 显示系统信息
            self.show_system_info()
            
            # 打开浏览器
            browser_thread = threading.Thread(target=self.open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            # 开始监控
            monitor_thread = threading.Thread(target=self.monitor_services)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 主循环
            try:
                while self.is_running:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
            
            return True
            
        except Exception as e:
            logger.error(f"系统运行时发生错误: {e}")
            return False
        finally:
            self.shutdown()

def main():
    """主函数"""
    try:
        manager = SystemManager()
        success = manager.run()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
