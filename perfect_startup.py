#!/usr/bin/env python3
"""
TCM家庭私人医生小帮手 - 完美启动脚本
一键启动所有服务，确保100%功能可用
"""

import subprocess
import time
import requests
import json
import sys
import os
from pathlib import Path
import signal

class TCMSystemLauncher:
    def __init__(self):
        self.processes = []
        self.base_dir = Path(__file__).parent
        self.backend_url = "http://localhost:5001"
        self.frontend_url = "http://localhost:3000"
        
    def log(self, message, level="INFO"):
        """统一日志输出"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def check_port(self, port):
        """检查端口是否被占用"""
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('localhost', port)) == 0
    
    def start_backend(self):
        """启动后端服务"""
        self.log("🚀 启动后端服务...")
        
        # 启动后端
        try:
            backend_process = subprocess.Popen(
                [sys.executable, "fastapi_backend.py"],
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            self.processes.append(("backend", backend_process))
            self.log("后端服务启动中...")
            
            # 等待后端启动
            for i in range(30):  # 最多等待30秒
                try:
                    response = requests.get(f"{self.backend_url}/api/health", timeout=5)
                    if response.status_code == 200:
                        health_data = response.json()
                        self.log(f"✅ 后端服务启动成功: {health_data['status']}")
                        self.log(f"   向量数据库: {health_data['vector_db_chunks']} 个文档块")
                        return True
                except:
                    pass
                
                time.sleep(1)
                if i % 5 == 4:  # 每5秒输出一次
                    self.log(f"等待后端启动... ({i+1}/30)")
            
            self.log("❌ 后端服务启动超时", "ERROR")
            return False
            
        except Exception as e:
            self.log(f"❌ 后端服务启动失败: {e}", "ERROR")
            return False
    
    def test_system(self):
        """测试系统功能"""
        self.log("🧪 测试系统功能...")
        
        tests = []
        
        # 测试后端健康检查
        try:
            response = requests.get(f"{self.backend_url}/api/health", timeout=10)
            if response.status_code == 200:
                tests.append(("后端健康检查", True))
            else:
                tests.append(("后端健康检查", False))
        except:
            tests.append(("后端健康检查", False))
        
        # 测试会话API
        try:
            response = requests.get(f"{self.backend_url}/api/sessions", timeout=15)
            if response.status_code == 200:
                sessions = response.json().get('sessions', [])
                tests.append((f"会话管理API ({len(sessions)}个会话)", True))
            else:
                tests.append(("会话管理API", False))
        except:
            tests.append(("会话管理API", False))
        
        # 测试文档API
        try:
            response = requests.get(f"{self.backend_url}/api/documents", timeout=10)
            if response.status_code == 200:
                docs = response.json().get('documents', [])
                tests.append((f"文档管理API ({len(docs)}个文档)", True))
            else:
                tests.append(("文档管理API", False))
        except:
            tests.append(("文档管理API", False))
        
        # 测试聊天API（快速测试）
        try:
            response = requests.post(
                f"{self.backend_url}/api/chat", 
                json={"message": "系统测试"}, 
                timeout=40
            )
            if response.status_code == 200:
                tests.append(("聊天API", True))
            else:
                tests.append(("聊天API", False))
        except:
            tests.append(("聊天API", False))
        
        # 输出测试结果
        self.log("📊 系统功能测试结果:")
        passed = 0
        for test_name, result in tests:
            status = "✅ 通过" if result else "❌ 失败"
            self.log(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        self.log(f"总体结果: {passed}/{len(tests)} 项测试通过")
        return passed, len(tests)
    
    def cleanup(self):
        """清理所有进程"""
        self.log("🧹 清理系统进程...")
        for name, process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
                self.log(f"已停止 {name} 服务")
            except:
                try:
                    process.kill()
                    self.log(f"已强制停止 {name} 服务")
                except:
                    pass
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.log("收到停止信号，正在清理...")
        self.cleanup()
        sys.exit(0)
    
    def run(self):
        """运行完整系统"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.log("🏥 TCM家庭私人医生小帮手 - 系统启动")
        self.log("=" * 50)
        
        try:
            # 启动后端
            if not self.start_backend():
                self.log("❌ 后端启动失败，无法继续", "ERROR")
                return False
            
            # 测试系统
            passed, total = self.test_system()
            
            # 输出启动结果
            self.log("=" * 50)
            if passed >= 3:  # 至少3个核心功能通过
                self.log("🎉 系统启动成功！")
                self.log(f"📱 后端服务: {self.backend_url}")
                self.log("⚠️ 前端服务需要单独启动: cd frontend && npm run serve")
                
                self.log("\n💡 使用说明:")
                self.log("   - 后端API已就绪，支持所有功能")
                self.log("   - 聊天API: POST /api/chat")
                self.log("   - 会话管理: GET /api/sessions")
                self.log("   - 文档管理: GET /api/documents")
                self.log("   - 健康检查: GET /api/health")
                self.log("   - 按 Ctrl+C 停止系统")
                
                # 保持运行
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    pass
                
                return True
            else:
                self.log("❌ 系统启动失败，关键功能不可用", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ 系统启动异常: {e}", "ERROR")
            return False
        finally:
            self.cleanup()

def main():
    launcher = TCMSystemLauncher()
    success = launcher.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
