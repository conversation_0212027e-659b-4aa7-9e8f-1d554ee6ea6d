#!/usr/bin/env python3
"""
系统完整性100%验证
对照TCM_README.md逐项验证所有功能
"""

import requests
import time
import json
import subprocess
import sys
from pathlib import Path

class SystemVerification:
    def __init__(self):
        self.backend_url = "http://localhost:5002"
        self.frontend_url = "http://localhost:3000"
        self.results = []
        
    def log(self, message, status="INFO"):
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {status}: {message}")
        
    def verify_item(self, name, test_func, required=True):
        """验证单个功能项"""
        try:
            self.log(f"🔍 验证: {name}")
            result = test_func()
            
            if result:
                self.log(f"✅ {name}: 通过", "PASS")
                self.results.append({"name": name, "status": "PASS", "required": required})
                return True
            else:
                self.log(f"❌ {name}: 失败", "FAIL")
                self.results.append({"name": name, "status": "FAIL", "required": required})
                return False
        except Exception as e:
            self.log(f"❌ {name}: 异常 - {e}", "ERROR")
            self.results.append({"name": name, "status": "ERROR", "required": required, "error": str(e)})
            return False
    
    def test_backend_health(self):
        """测试后端健康检查"""
        try:
            response = requests.get(f"{self.backend_url}/api/health", timeout=10)
            return response.status_code == 200 and response.json()['status'] == 'healthy'
        except:
            return False
    
    def test_frontend_access(self):
        """测试前端访问"""
        try:
            response = requests.get(self.frontend_url, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def test_chat_api(self):
        """测试聊天API"""
        try:
            response = requests.post(
                f"{self.backend_url}/api/chat",
                json={"message": "系统验证测试"},
                timeout=30
            )
            if response.status_code == 200:
                data = response.json()
                return 'response' in data and 'session_id' in data
            return False
        except:
            return False
    
    def test_sessions_api(self):
        """测试会话管理API"""
        try:
            response = requests.get(f"{self.backend_url}/api/sessions", timeout=15)
            if response.status_code == 200:
                data = response.json()
                return 'sessions' in data
            return False
        except:
            return False
    
    def test_documents_api(self):
        """测试文档管理API"""
        try:
            response = requests.get(f"{self.backend_url}/api/documents", timeout=10)
            if response.status_code == 200:
                data = response.json()
                return 'documents' in data and 'total_count' in data
            return False
        except:
            return False
    
    def test_vue_cli_startup(self):
        """测试Vue CLI启动"""
        try:
            # 检查前端目录
            frontend_dir = Path("frontend")
            if not frontend_dir.exists():
                return False
            
            # 检查package.json
            package_json = frontend_dir / "package.json"
            if not package_json.exists():
                return False
            
            # 检查node_modules
            node_modules = frontend_dir / "node_modules"
            if not node_modules.exists():
                return False
            
            return True
        except:
            return False
    
    def test_performance_requirement(self):
        """测试性能要求（<30秒）"""
        try:
            start_time = time.time()
            response = requests.post(
                f"{self.backend_url}/api/chat",
                json={"message": "性能测试"},
                timeout=35
            )
            response_time = time.time() - start_time
            
            return response.status_code == 200 and response_time <= 30
        except:
            return False
    
    def test_cors_configuration(self):
        """测试CORS配置"""
        try:
            response = requests.options(f"{self.backend_url}/api/health", timeout=10)
            return response.status_code in [200, 204]
        except:
            return False
    
    def test_error_handling(self):
        """测试错误处理"""
        try:
            # 测试空消息
            response = requests.post(
                f"{self.backend_url}/api/chat",
                json={"message": ""},
                timeout=10
            )
            return response.status_code == 400
        except:
            return False
    
    def test_session_persistence(self):
        """测试会话持久化"""
        try:
            # 创建会话
            response1 = requests.post(
                f"{self.backend_url}/api/chat",
                json={"message": "测试会话1"},
                timeout=30
            )
            
            if response1.status_code != 200:
                return False
            
            session_id = response1.json()['session_id']
            
            # 继续会话
            response2 = requests.post(
                f"{self.backend_url}/api/chat",
                json={"message": "测试会话2", "session_id": session_id},
                timeout=30
            )
            
            return response2.status_code == 200 and response2.json()['session_id'] == session_id
        except:
            return False
    
    def run_verification(self):
        """运行完整验证"""
        self.log("🚀 开始系统完整性验证")
        self.log("=" * 60)
        
        # 核心功能验证（必需）
        self.verify_item("后端健康检查API", self.test_backend_health, required=True)
        self.verify_item("前端界面访问", self.test_frontend_access, required=True)
        self.verify_item("聊天API功能", self.test_chat_api, required=True)
        self.verify_item("会话管理API", self.test_sessions_api, required=True)
        self.verify_item("文档管理API", self.test_documents_api, required=True)
        
        # Vue.js前端验证
        self.verify_item("Vue CLI环境", self.test_vue_cli_startup, required=True)
        
        # 性能要求验证
        self.verify_item("响应时间<30秒", self.test_performance_requirement, required=True)
        
        # 技术规范验证
        self.verify_item("CORS跨域配置", self.test_cors_configuration, required=True)
        self.verify_item("错误处理机制", self.test_error_handling, required=True)
        self.verify_item("会话持久化", self.test_session_persistence, required=True)
        
        # 生成验证报告
        self.generate_report()
    
    def generate_report(self):
        """生成验证报告"""
        self.log("=" * 60)
        self.log("📊 系统完整性验证报告")
        
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.results if r['status'] in ['FAIL', 'ERROR']])
        
        required_tests = [r for r in self.results if r['required']]
        required_passed = len([r for r in required_tests if r['status'] == 'PASS'])
        
        self.log(f"总测试项: {total_tests}")
        self.log(f"通过测试: {passed_tests}")
        self.log(f"失败测试: {failed_tests}")
        self.log(f"必需功能通过率: {required_passed}/{len(required_tests)} ({required_passed/len(required_tests)*100:.1f}%)")
        
        # 详细结果
        self.log("\n📋 详细测试结果:")
        for result in self.results:
            status_icon = "✅" if result['status'] == 'PASS' else "❌"
            required_mark = "[必需]" if result['required'] else "[可选]"
            self.log(f"   {status_icon} {result['name']} {required_mark}")
            
            if result['status'] == 'ERROR' and 'error' in result:
                self.log(f"      错误: {result['error']}")
        
        # 总体评估
        self.log("\n🎯 总体评估:")
        if required_passed == len(required_tests):
            self.log("🎉 系统完整性验证通过！所有必需功能正常工作")
            self.log("✅ 系统已达到生产级别要求")
            return True
        else:
            self.log("❌ 系统完整性验证失败！存在必需功能问题")
            self.log("⚠️ 需要修复失败的必需功能")
            return False

def main():
    verifier = SystemVerification()
    success = verifier.run_verification()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
