{"project_name": "家庭私人医生小帮手 v2.0.0", "architecture": "Vue.js + Flask + RAG + MCP", "core_files": {"flask_backend.py": {"size": 28411, "modified": "2025-06-23T10:18:01.214245"}, "intelligent_mcp_service.py": {"size": 18793, "modified": "2025-06-22T20:45:40.135546"}, "perfect_startup.py": {"size": 16357, "modified": "2025-06-23T10:50:20.807817"}, "requirements.txt": {"size": 764, "modified": "2025-06-23T11:49:16.869294"}}, "directories": {"documents": {"exists": true, "file_count": 7}, "uploads": {"exists": true, "file_count": 1}, "conversations": {"exists": true, "file_count": 14}, "vector_db": {"exists": true, "file_count": 0}, "frontend": {"exists": true, "file_count": 20612}}, "statistics": {"total_python_files": 14, "total_directories": 9, "cleanup_timestamp": "2025-06-23T11:58:56.732315"}}