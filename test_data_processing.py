#!/usr/bin/env python3
"""
测试数据处理功能
"""

import requests
import json
import os
from pathlib import Path

def test_vector_database():
    """测试向量数据库"""
    print("🔍 测试向量数据库...")
    
    try:
        response = requests.get("http://localhost:5000/api/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            vector_chunks = health_data.get('vector_db_chunks', 0)
            print(f"✅ 向量数据库状态: {vector_chunks}个文档块")
            
            if vector_chunks >= 290:
                print("✅ 向量数据库完整")
                return True
            else:
                print(f"⚠️ 向量数据库不完整，期望290块，实际{vector_chunks}块")
                return False
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 向量数据库测试失败: {e}")
        return False

def test_document_management():
    """测试文档管理"""
    print("🔍 测试文档管理...")
    
    try:
        response = requests.get("http://localhost:5000/api/documents", timeout=10)
        if response.status_code == 200:
            doc_data = response.json()
            documents = doc_data.get('documents', [])
            total_docs = len(documents)
            
            print(f"✅ 文档管理正常: {total_docs}个文档")
            
            # 统计文档类型
            pdf_count = len([d for d in documents if d['name'].endswith('.pdf')])
            txt_count = len([d for d in documents if d['name'].endswith('.txt')])
            docx_count = len([d for d in documents if d['name'].endswith('.docx')])
            
            print(f"📄 文档类型分布: PDF({pdf_count}), TXT({txt_count}), DOCX({docx_count})")
            
            return total_docs > 0
        else:
            print(f"❌ 文档管理异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 文档管理测试失败: {e}")
        return False

def test_rag_retrieval():
    """测试RAG检索功能"""
    print("🔍 测试RAG检索功能...")
    
    try:
        # 测试聊天功能，这会触发RAG检索
        test_query = "栀子甘草豉汤方的组成是什么？"
        
        chat_data = {
            'message': test_query,
            'session_id': None
        }
        
        response = requests.post(
            'http://localhost:5000/api/chat',
            json=chat_data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result.get('response', '')
            
            if ai_response and len(ai_response) > 50:
                print(f"✅ RAG检索成功")
                print(f"📝 AI回答长度: {len(ai_response)}字符")
                print(f"📄 回答预览: {ai_response[:150]}...")
                
                # 检查是否包含相关信息
                if '栀子' in ai_response or '甘草' in ai_response or '豉汤' in ai_response:
                    print("✅ 检索到相关中医信息")
                    return True
                else:
                    print("⚠️ 回答可能不够相关")
                    return False
            else:
                print("❌ AI回答为空或过短")
                return False
        else:
            print(f"❌ 聊天API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ RAG检索测试失败: {e}")
        return False

def test_mcp_service():
    """测试MCP服务"""
    print("🔍 测试MCP服务...")
    
    try:
        response = requests.get("http://localhost:8006/health", timeout=10)
        if response.status_code == 200:
            mcp_data = response.json()
            print(f"✅ MCP服务正常: {mcp_data.get('service', 'Unknown')}")
            return True
        else:
            print(f"❌ MCP服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ MCP服务测试失败: {e}")
        return False

def test_embedding_model():
    """测试嵌入模型"""
    print("🔍 测试m3e-base嵌入模型...")
    
    # 检查模型文件是否存在
    model_path = Path("models/m3e-base")
    if model_path.exists():
        print(f"✅ m3e-base模型文件存在: {model_path}")
        
        # 检查模型文件
        model_files = list(model_path.rglob("*"))
        if model_files:
            print(f"✅ 模型文件数量: {len(model_files)}")
            return True
        else:
            print("❌ 模型目录为空")
            return False
    else:
        print("❌ m3e-base模型文件不存在")
        return False

def main():
    """主测试函数"""
    print("🧪 开始数据处理功能测试...")
    print("=" * 50)
    
    tests = [
        ("向量数据库", test_vector_database),
        ("文档管理", test_document_management),
        ("RAG检索", test_rag_retrieval),
        ("MCP服务", test_mcp_service),
        ("嵌入模型", test_embedding_model)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        if test_func():
            print(f"✅ {test_name} - 通过")
            passed += 1
        else:
            print(f"❌ {test_name} - 失败")
    
    print("\n" + "=" * 50)
    print(f"📊 数据处理测试结果: {passed}/{total} 通过")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有数据处理功能正常！")
    else:
        print("⚠️ 部分功能需要检查")

if __name__ == '__main__':
    main()
