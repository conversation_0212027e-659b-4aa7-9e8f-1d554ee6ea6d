<template>
  <div id="app">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success shadow-sm">
      <div class="container-fluid">
        <router-link class="navbar-brand d-flex align-items-center" to="/">
          <i class="bi bi-heart-pulse-fill me-2"></i>
          <span class="fw-bold">🏥 家庭私人医生小帮手</span>
        </router-link>
        
        <button 
          class="navbar-toggler" 
          type="button" 
          data-bs-toggle="collapse" 
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <router-link class="nav-link" to="/">
                <i class="bi bi-chat-dots me-1"></i>智能咨询
              </router-link>
            </li>
            <li class="nav-item">
              <router-link class="nav-link" to="/documents">
                <i class="bi bi-file-earmark-medical me-1"></i>文档管理
              </router-link>
            </li>
            <li class="nav-item">
              <router-link class="nav-link" to="/sessions">
                <i class="bi bi-clock-history me-1"></i>历史记录
              </router-link>
            </li>
            <li class="nav-item">
              <router-link class="nav-link" to="/settings">
                <i class="bi bi-gear me-1"></i>设置
              </router-link>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- 系统状态指示器 -->
    <div v-if="systemStatus" class="container-fluid mt-2">
      <div class="row">
        <div class="col-12">
          <div 
            :class="['alert', systemStatus.status === 'healthy' ? 'alert-success' : 'alert-warning']" 
            class="py-2 mb-2"
          >
            <div class="d-flex justify-content-between align-items-center">
              <div class="d-flex align-items-center">
                <i :class="systemStatus.status === 'healthy' ? 'bi-check-circle-fill' : 'bi-exclamation-triangle-fill'" class="me-2"></i>
                <small>
                  系统状态: {{ systemStatus.status === 'healthy' ? '正常' : '初始化中' }}
                  | 向量数据库: {{ systemStatus.vector_db_chunks || 0 }} 个文档块
                  | 版本: {{ systemStatus.version }}
                </small>
              </div>
              <small class="text-muted">
                {{ formatTime(systemStatus.timestamp) }}
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="container-fluid flex-grow-1">
      <router-view />
    </main>

    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-4">
      <div class="container">
        <small class="text-muted">
          🏥 家庭私人医生小帮手 v2.0.0 |
          基于Vue.js + FastAPI架构 |
          <i class="bi bi-shield-check"></i> 专业中医智能咨询
        </small>
      </div>
    </footer>

    <!-- 全局加载指示器 -->
    <div v-if="globalLoading" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.5); z-index: 9999;">
      <div class="bg-white rounded p-4 text-center">
        <div class="spinner-border text-success mb-3" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <div>{{ loadingMessage || '正在处理，请稍候...' }}</div>
      </div>
    </div>

    <!-- 全局通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
      <div 
        v-for="notification in notifications" 
        :key="notification.id"
        :class="['toast', 'show']"
        role="alert"
      >
        <div class="toast-header">
          <i :class="getNotificationIcon(notification.type)" class="me-2"></i>
          <strong class="me-auto">{{ getNotificationTitle(notification.type) }}</strong>
          <small>{{ formatTime(notification.timestamp) }}</small>
          <button 
            type="button" 
            class="btn-close" 
            @click="removeNotification(notification.id)"
          ></button>
        </div>
        <div class="toast-body">
          {{ notification.message }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import apiService from './services/api'

export default {
  name: 'App',
  setup() {
    const systemStatus = ref(null)
    const globalLoading = ref(false)
    const loadingMessage = ref('')
    const notifications = ref([])
    let statusCheckInterval = null

    // 检查系统状态
    const checkSystemStatus = async () => {
      try {
        const response = await apiService.checkHealth()
        systemStatus.value = response.data
      } catch (error) {
        console.error('系统状态检查失败:', error)
        systemStatus.value = {
          status: 'error',
          timestamp: new Date().toISOString(),
          vector_db_chunks: 0,
          version: 'unknown'
        }
      }
    }

    // 显示通知
    const showNotification = (type, message) => {
      const notification = {
        id: Date.now(),
        type,
        message,
        timestamp: new Date().toISOString()
      }
      notifications.value.push(notification)
      
      // 自动移除通知
      setTimeout(() => {
        removeNotification(notification.id)
      }, 5000)
    }

    // 移除通知
    const removeNotification = (id) => {
      const index = notifications.value.findIndex(n => n.id === id)
      if (index > -1) {
        notifications.value.splice(index, 1)
      }
    }

    // 获取通知图标
    const getNotificationIcon = (type) => {
      const icons = {
        success: 'bi-check-circle-fill text-success',
        error: 'bi-exclamation-circle-fill text-danger',
        warning: 'bi-exclamation-triangle-fill text-warning',
        info: 'bi-info-circle-fill text-info'
      }
      return icons[type] || icons.info
    }

    // 获取通知标题
    const getNotificationTitle = (type) => {
      const titles = {
        success: '成功',
        error: '错误',
        warning: '警告',
        info: '信息'
      }
      return titles[type] || '通知'
    }

    // 格式化时间
    const formatTime = (timestamp) => {
      return window.formatTime(timestamp)
    }

    // 设置全局加载状态
    const setGlobalLoading = (loading, message = '') => {
      globalLoading.value = loading
      loadingMessage.value = message
    }

    onMounted(() => {
      // 初始检查系统状态
      checkSystemStatus()
      
      // 定期检查系统状态
      statusCheckInterval = setInterval(checkSystemStatus, 30000) // 每30秒检查一次
      
      // 全局事件监听
      window.addEventListener('online', () => {
        showNotification('success', '网络连接已恢复')
        checkSystemStatus()
      })
      
      window.addEventListener('offline', () => {
        showNotification('warning', '网络连接已断开')
      })
    })

    onUnmounted(() => {
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval)
      }
    })

    // 提供给子组件使用的方法
    window.showNotification = showNotification
    window.setGlobalLoading = setGlobalLoading

    return {
      systemStatus,
      globalLoading,
      loadingMessage,
      notifications,
      removeNotification,
      getNotificationIcon,
      getNotificationTitle,
      formatTime
    }
  }
}
</script>

<style>
/* 全局样式已在index.html中定义 */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

/* 移动端导航栏优化 */
@media (max-width: 768px) {
  .navbar-brand {
    font-size: 1rem;
  }
  
  .navbar-nav .nav-link {
    padding: 0.5rem 1rem;
  }
}

/* 通知样式 */
.toast {
  min-width: 300px;
}

@media (max-width: 768px) {
  .toast {
    min-width: 250px;
  }
  
  .toast-container {
    padding: 1rem;
  }
}
</style>
