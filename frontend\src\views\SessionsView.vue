<template>
  <div class="sessions-view">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header bg-info text-white">
            <div class="d-flex justify-content-between align-items-center">
              <div class="d-flex align-items-center">
                <i class="bi bi-clock-history me-2"></i>
                <h5 class="mb-0">📋 对话历史</h5>
              </div>
              <div class="btn-group">
                <router-link
                  to="/"
                  class="btn btn-light btn-sm me-2"
                  style="text-decoration: none;"
                >
                  <i class="bi bi-plus-circle me-1"></i>开始新对话
                </router-link>
                <button
                  class="btn btn-outline-light btn-sm"
                  @click="refreshSessions"
                  :disabled="isLoading"
                >
                  <i class="bi bi-arrow-clockwise me-1"></i>刷新
                </button>
                <button
                  class="btn btn-outline-light btn-sm"
                  @click="exportSessions"
                  :disabled="sessions.length === 0"
                >
                  <i class="bi bi-download me-1"></i>导出
                </button>
              </div>
            </div>
          </div>

          <div class="card-body">
            <!-- 快速开始新对话 -->
            <div class="alert alert-success d-flex align-items-center mb-4" role="alert">
              <i class="bi bi-chat-heart-fill me-3 fs-4"></i>
              <div class="flex-grow-1">
                <h6 class="alert-heading mb-1">💬 想要开始新的健康咨询？</h6>
                <p class="mb-2">点击下方按钮，立即与AI医生开始智能对话</p>
              </div>
              <router-link to="/" class="btn btn-success btn-lg">
                <i class="bi bi-chat-dots me-2"></i>开始聊天
              </router-link>
            </div>

            <!-- 搜索和筛选 -->
            <div class="row mb-4">
              <div class="col-md-8">
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="bi bi-search"></i>
                  </span>
                  <input 
                    v-model="searchQuery"
                    type="text" 
                    class="form-control"
                    placeholder="搜索对话内容..."
                  >
                </div>
              </div>
              <div class="col-md-4">
                <select v-model="sortBy" class="form-select">
                  <option value="created_at">按创建时间</option>
                  <option value="last_activity">按最后活动</option>
                  <option value="message_count">按消息数量</option>
                </select>
              </div>
            </div>

            <!-- 会话统计 -->
            <div class="row mb-4">
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-primary text-white">
                  <div class="card-body text-center">
                    <i class="bi bi-chat-dots display-4 mb-2"></i>
                    <h5>{{ sessions.length }}</h5>
                    <small>总对话数</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-success text-white">
                  <div class="card-body text-center">
                    <i class="bi bi-chat-text display-4 mb-2"></i>
                    <h5>{{ totalMessages }}</h5>
                    <small>总消息数</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-info text-white">
                  <div class="card-body text-center">
                    <i class="bi bi-calendar-week display-4 mb-2"></i>
                    <h5>{{ recentSessions }}</h5>
                    <small>本周对话</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-warning text-white">
                  <div class="card-body text-center">
                    <i class="bi bi-clock display-4 mb-2"></i>
                    <h5>{{ formatTime(lastActivity) }}</h5>
                    <small>最后活动</small>
                  </div>
                </div>
              </div>
            </div>

            <!-- 会话列表 -->
            <div class="row">
              <div class="col-12">
                <div v-if="isLoading" class="text-center py-5">
                  <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">加载中...</span>
                  </div>
                  <div>正在加载对话历史...</div>
                </div>

                <div v-else-if="filteredSessions.length === 0" class="text-center py-5 text-muted">
                  <i class="bi bi-inbox display-1 mb-3"></i>
                  <h4>暂无对话记录</h4>
                  <p>开始您的第一次智能咨询吧！</p>
                  <router-link to="/" class="btn btn-primary">
                    <i class="bi bi-chat-dots me-1"></i>开始对话
                  </router-link>
                </div>

                <div v-else class="row">
                  <div 
                    v-for="session in filteredSessions" 
                    :key="session.session_id"
                    class="col-lg-6 col-xl-4 mb-4"
                  >
                    <div class="card h-100 session-card" @click="openSession(session)">
                      <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                          <i class="bi bi-chat-square-text text-primary me-2"></i>
                          <small class="text-muted">{{ getSessionTitle(session) }}</small>
                        </div>
                        <div class="dropdown" @click.stop>
                          <button 
                            class="btn btn-sm btn-outline-secondary dropdown-toggle"
                            type="button"
                            :id="`dropdown-${session.session_id}`"
                            data-bs-toggle="dropdown"
                          >
                            <i class="bi bi-three-dots"></i>
                          </button>
                          <ul class="dropdown-menu">
                            <li>
                              <a class="dropdown-item" href="#" @click="openSession(session)">
                                <i class="bi bi-eye me-2"></i>查看详情
                              </a>
                            </li>
                            <li>
                              <a class="dropdown-item" href="#" @click="exportSession(session)">
                                <i class="bi bi-download me-2"></i>导出对话
                              </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                              <a class="dropdown-item text-danger" href="#" @click="deleteSession(session)">
                                <i class="bi bi-trash me-2"></i>删除对话
                              </a>
                            </li>
                          </ul>
                        </div>
                      </div>

                      <div class="card-body">
                        <div class="mb-3">
                          <div class="session-preview">
                            {{ session.preview || '暂无内容预览' }}
                          </div>
                        </div>

                        <div class="row text-center">
                          <div class="col-4">
                            <div class="text-primary">
                              <i class="bi bi-chat-text"></i>
                              <div class="small">{{ session.message_count }}</div>
                              <div class="small text-muted">消息</div>
                            </div>
                          </div>
                          <div class="col-4">
                            <div class="text-success">
                              <i class="bi bi-calendar3"></i>
                              <div class="small">{{ formatDate(session.created_at) }}</div>
                              <div class="small text-muted">创建</div>
                            </div>
                          </div>
                          <div class="col-4">
                            <div class="text-info">
                              <i class="bi bi-clock"></i>
                              <div class="small">{{ formatDate(session.last_activity) }}</div>
                              <div class="small text-muted">活动</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="card-footer bg-transparent">
                        <div class="d-flex justify-content-between align-items-center">
                          <small class="text-muted">
                            ID: {{ session.session_id.split('_').pop() }}
                          </small>
                          <div class="btn-group btn-group-sm">
                            <button 
                              class="btn btn-outline-primary"
                              @click.stop="openSession(session)"
                              title="继续对话"
                            >
                              <i class="bi bi-arrow-right"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import apiService from '../services/api'

export default {
  name: 'SessionsView',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const sessions = ref([])
    const isLoading = ref(false)
    const searchQuery = ref('')
    const sortBy = ref('last_activity')

    // 计算属性
    const filteredSessions = computed(() => {
      let filtered = sessions.value

      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(session => 
          session.preview.toLowerCase().includes(query) ||
          session.session_id.toLowerCase().includes(query)
        )
      }

      // 排序
      filtered.sort((a, b) => {
        const aValue = a[sortBy.value]
        const bValue = b[sortBy.value]
        
        if (sortBy.value === 'message_count') {
          return bValue - aValue
        } else {
          return new Date(bValue) - new Date(aValue)
        }
      })

      return filtered
    })

    const totalMessages = computed(() => {
      return sessions.value.reduce((sum, session) => sum + session.message_count, 0)
    })

    const recentSessions = computed(() => {
      const oneWeekAgo = new Date()
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
      
      return sessions.value.filter(session => 
        new Date(session.created_at) > oneWeekAgo
      ).length
    })

    const lastActivity = computed(() => {
      if (sessions.value.length === 0) return new Date().toISOString()
      
      const latest = sessions.value.reduce((latest, session) => {
        return new Date(session.last_activity) > new Date(latest) 
          ? session.last_activity 
          : latest
      }, sessions.value[0].last_activity)
      
      return latest
    })

    // 方法
    const formatTime = (timestamp) => {
      return window.formatTime(timestamp)
    }

    const formatDate = (timestamp) => {
      const date = new Date(timestamp)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 1) return '今天'
      if (diffDays === 2) return '昨天'
      if (diffDays <= 7) return `${diffDays}天前`
      
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit'
      })
    }

    const getSessionTitle = (session) => {
      const date = new Date(session.created_at)
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const loadSessions = async () => {
      try {
        isLoading.value = true
        const response = await apiService.getSessions()
        sessions.value = response.data.sessions || []
      } catch (error) {
        console.error('加载会话列表失败:', error)
        window.showNotification('error', `加载会话失败: ${error.message}`)
      } finally {
        isLoading.value = false
      }
    }

    const refreshSessions = () => {
      loadSessions()
    }

    const openSession = (session) => {
      router.push(`/session/${session.session_id}`)
    }

    const deleteSession = async (session) => {
      if (!confirm(`确定要删除对话 "${getSessionTitle(session)}" 吗？此操作不可恢复。`)) {
        return
      }

      try {
        await apiService.deleteSession(session.session_id)
        window.showNotification('success', '对话删除成功')
        await loadSessions()
      } catch (error) {
        console.error('删除会话失败:', error)
        window.showNotification('error', `删除失败: ${error.message}`)
      }
    }

    const exportSession = (session) => {
      // 简单的导出功能
      const data = {
        session_id: session.session_id,
        created_at: session.created_at,
        last_activity: session.last_activity,
        message_count: session.message_count,
        preview: session.preview
      }
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `session_${session.session_id}.json`
      a.click()
      URL.revokeObjectURL(url)
      
      window.showNotification('success', '对话导出成功')
    }

    const exportSessions = () => {
      const data = {
        export_time: new Date().toISOString(),
        total_sessions: sessions.value.length,
        sessions: sessions.value
      }
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `all_sessions_${new Date().toISOString().split('T')[0]}.json`
      a.click()
      URL.revokeObjectURL(url)
      
      window.showNotification('success', '所有对话导出成功')
    }

    // 生命周期
    onMounted(() => {
      loadSessions()
    })

    return {
      sessions,
      isLoading,
      searchQuery,
      sortBy,
      filteredSessions,
      totalMessages,
      recentSessions,
      lastActivity,
      formatTime,
      formatDate,
      getSessionTitle,
      refreshSessions,
      openSession,
      deleteSession,
      exportSession,
      exportSessions
    }
  }
}
</script>

<style scoped>
.session-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.session-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.session-preview {
  font-size: 0.9rem;
  color: #6c757d;
  height: 3rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

@media (max-width: 768px) {
  .session-card {
    margin-bottom: 1rem;
  }
  
  .card-body .row .col-4 {
    margin-bottom: 0.5rem;
  }
}
</style>
