#!/usr/bin/env python3
"""
🏥 家庭私人医生小帮手 - 聊天功能测试脚本
验证聊天系统的完整功能

功能测试:
- 前端界面可访问性
- 后端API端点功能
- MCP服务集成
- 聊天消息发送和接收
- 会话管理功能

作者: TCM RAG System Team
版本: 2.0.1
"""

import requests
import json
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ChatFunctionalityTester:
    """聊天功能测试器"""
    
    def __init__(self):
        self.base_url = 'http://localhost:5001'
        self.frontend_url = 'http://localhost:3000'
        self.mcp_url = 'http://localhost:8006'
        self.session_id = None
        
    def test_frontend_accessibility(self):
        """测试前端可访问性"""
        logger.info("🌐 测试前端界面可访问性...")
        try:
            response = requests.get(self.frontend_url, timeout=10)
            if response.status_code == 200:
                logger.info("✅ 前端界面可正常访问")
                return True
            else:
                logger.error(f"❌ 前端访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ 前端访问异常: {e}")
            return False
    
    def test_backend_health(self):
        """测试后端健康状态"""
        logger.info("🔧 测试后端API健康状态...")
        try:
            response = requests.get(f'{self.base_url}/api/health', timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                logger.info(f"✅ 后端健康状态: {health_data['status']}")
                logger.info(f"   向量数据库块数: {health_data.get('vector_db_chunks', 0)}")
                logger.info(f"   服务版本: {health_data.get('version', 'unknown')}")
                return True
            else:
                logger.error(f"❌ 后端健康检查失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ 后端健康检查异常: {e}")
            return False
    
    def test_mcp_service(self):
        """测试MCP服务"""
        logger.info("🤖 测试MCP智能服务...")
        try:
            response = requests.get(f'{self.mcp_url}/health', timeout=10)
            if response.status_code == 200:
                mcp_data = response.json()
                logger.info(f"✅ MCP服务状态: {mcp_data['status']}")
                logger.info(f"   服务版本: {mcp_data.get('version', 'unknown')}")
                return True
            else:
                logger.error(f"❌ MCP服务检查失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ MCP服务检查异常: {e}")
            return False
    
    def test_chat_message(self, message):
        """测试聊天消息发送"""
        logger.info(f"💬 测试聊天消息: '{message}'")
        try:
            payload = {
                'message': message,
                'session_id': self.session_id
            }
            
            response = requests.post(
                f'{self.base_url}/api/chat',
                json=payload,
                timeout=60  # 增加超时时间以适应AI响应
            )
            
            if response.status_code == 200:
                chat_data = response.json()
                self.session_id = chat_data.get('session_id')
                
                logger.info("✅ 聊天消息发送成功")
                logger.info(f"   会话ID: {self.session_id}")
                logger.info(f"   响应时间: {chat_data.get('response_time', 0):.2f}秒")
                logger.info(f"   AI回复: {chat_data.get('response', '')[:100]}...")
                
                return True
            else:
                logger.error(f"❌ 聊天消息发送失败，状态码: {response.status_code}")
                logger.error(f"   错误信息: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 聊天消息发送异常: {e}")
            return False
    
    def test_session_management(self):
        """测试会话管理功能"""
        logger.info("📋 测试会话管理功能...")
        try:
            # 获取会话列表
            response = requests.get(f'{self.base_url}/api/sessions', timeout=10)
            if response.status_code == 200:
                sessions_data = response.json()
                logger.info(f"✅ 会话列表获取成功，共 {len(sessions_data.get('sessions', []))} 个会话")
                
                # 如果有当前会话，测试获取会话详情
                if self.session_id:
                    detail_response = requests.get(
                        f'{self.base_url}/api/sessions/{self.session_id}',
                        timeout=10
                    )
                    if detail_response.status_code == 200:
                        session_detail = detail_response.json()
                        messages_count = len(session_detail.get('session_data', {}).get('messages', []))
                        logger.info(f"✅ 会话详情获取成功，包含 {messages_count} 条消息")
                        return True
                    else:
                        logger.error(f"❌ 会话详情获取失败，状态码: {detail_response.status_code}")
                        return False
                else:
                    logger.info("✅ 会话管理基础功能正常")
                    return True
            else:
                logger.error(f"❌ 会话列表获取失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 会话管理测试异常: {e}")
            return False
    
    def test_document_upload_api(self):
        """测试文档上传API"""
        logger.info("📄 测试文档管理API...")
        try:
            response = requests.get(f'{self.base_url}/api/documents', timeout=10)
            if response.status_code == 200:
                docs_data = response.json()
                logger.info(f"✅ 文档列表获取成功，共 {len(docs_data.get('documents', []))} 个文档")
                return True
            else:
                logger.error(f"❌ 文档列表获取失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ 文档管理API测试异常: {e}")
            return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("🚀 开始TCM聊天系统综合功能测试")
        logger.info("="*60)
        
        test_results = {}
        
        # 1. 前端可访问性测试
        test_results['frontend'] = self.test_frontend_accessibility()
        
        # 2. 后端健康状态测试
        test_results['backend'] = self.test_backend_health()
        
        # 3. MCP服务测试
        test_results['mcp'] = self.test_mcp_service()
        
        # 4. 聊天功能测试
        test_messages = [
            "你好，我是新用户",
            "肾虚怎么治疗？",
            "栀子甘草豉汤方的组成是什么？"
        ]
        
        chat_results = []
        for message in test_messages:
            result = self.test_chat_message(message)
            chat_results.append(result)
            time.sleep(2)  # 避免请求过于频繁
        
        test_results['chat'] = all(chat_results)
        
        # 5. 会话管理测试
        test_results['sessions'] = self.test_session_management()
        
        # 6. 文档管理API测试
        test_results['documents'] = self.test_document_upload_api()
        
        # 生成测试报告
        self.generate_test_report(test_results)
        
        return all(test_results.values())
    
    def generate_test_report(self, results):
        """生成测试报告"""
        logger.info("="*60)
        logger.info("📊 TCM聊天系统功能测试报告")
        logger.info("="*60)
        
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            test_display_name = {
                'frontend': '前端界面访问',
                'backend': '后端API服务',
                'mcp': 'MCP智能服务',
                'chat': '聊天功能',
                'sessions': '会话管理',
                'documents': '文档管理'
            }.get(test_name, test_name)
            
            logger.info(f"  {test_display_name}: {status}")
        
        logger.info("-" * 60)
        logger.info(f"测试总结: {passed_tests}/{total_tests} 项测试通过")
        
        if passed_tests == total_tests:
            logger.info("🎉 所有功能测试通过！聊天系统运行正常！")
        else:
            logger.warning(f"⚠️ 有 {total_tests - passed_tests} 项测试失败，需要进一步检查")
        
        logger.info("="*60)

def main():
    """主函数"""
    tester = ChatFunctionalityTester()
    success = tester.run_comprehensive_test()
    
    if success:
        print("\n🏥 家庭私人医生小帮手聊天系统功能验证完成！")
        print("💡 您现在可以在浏览器中访问 http://localhost:3000 开始使用聊天功能")
    else:
        print("\n❌ 聊天系统功能验证发现问题，请检查上述错误信息")
    
    return success

if __name__ == '__main__':
    main()
