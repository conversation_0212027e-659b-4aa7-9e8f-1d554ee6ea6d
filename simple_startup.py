#!/usr/bin/env python3
"""
🏥 家庭私人医生小帮手 - 简化启动脚本
解决聊天功能缺失问题的专用启动器

功能特性:
- 逐步启动各个服务
- 详细的错误诊断
- 端口冲突检测和解决
- 服务健康状态监控

作者: TCM RAG System Team
版本: 2.0.1
"""

import os
import sys
import time
import subprocess
import logging
import signal
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('simple_startup.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class SimpleSystemManager:
    """简化的系统管理器"""
    
    def __init__(self):
        self.processes = {}
        self.base_path = Path.cwd()
        self.frontend_path = self.base_path / 'frontend'
        
        # 服务配置
        self.services = {
            'mcp_service': {
                'name': 'MCP智能服务',
                'command': [sys.executable, 'intelligent_mcp_service.py'],
                'port': 8006,
                'cwd': str(self.base_path)
            },
            'fastapi_backend': {
                'name': 'FastAPI后端服务',
                'command': [sys.executable, 'fastapi_backend.py'],
                'port': 5001,
                'cwd': str(self.base_path)
            },
            'vue_frontend': {
                'name': 'Vue.js前端服务',
                'command': ['npm', 'run', 'serve'],
                'port': 3000,
                'cwd': str(self.frontend_path)
            }
        }
    
    def kill_port_process(self, port):
        """终止占用指定端口的进程"""
        try:
            logger.info(f"检查端口 {port} 占用情况...")
            result = subprocess.run(
                ['netstat', '-ano'], 
                capture_output=True, 
                text=True,
                shell=True
            )
            
            for line in result.stdout.split('\n'):
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.strip().split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        logger.info(f"发现端口 {port} 被进程 {pid} 占用，正在终止...")
                        subprocess.run(['taskkill', '/F', '/PID', pid], 
                                     capture_output=True, shell=True)
                        logger.info(f"已终止进程 {pid}")
                        time.sleep(1)
        except Exception as e:
            logger.warning(f"清理端口 {port} 时发生错误: {e}")
    
    def start_service(self, service_name):
        """启动单个服务"""
        config = self.services[service_name]
        logger.info(f"🚀 启动 {config['name']}...")
        
        # 清理端口
        self.kill_port_process(config['port'])
        
        try:
            # 启动进程
            process = subprocess.Popen(
                config['command'],
                cwd=config['cwd'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                shell=True
            )
            
            self.processes[service_name] = {
                'process': process,
                'config': config
            }
            
            logger.info(f"✅ {config['name']} 启动成功 (PID: {process.pid})")
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动 {config['name']} 时发生错误: {e}")
            return False
    
    def check_service_health(self, service_name):
        """检查服务健康状态"""
        config = self.services[service_name]
        port = config['port']
        
        try:
            import requests
            if service_name == 'mcp_service':
                url = f'http://localhost:{port}/health'
            elif service_name == 'fastapi_backend':
                url = f'http://localhost:{port}/api/health'
            else:  # vue_frontend
                url = f'http://localhost:{port}'
            
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def wait_for_service(self, service_name, max_wait=30):
        """等待服务就绪"""
        config = self.services[service_name]
        logger.info(f"⏳ 等待 {config['name']} 就绪...")
        
        for i in range(max_wait):
            if self.check_service_health(service_name):
                logger.info(f"✅ {config['name']} 就绪")
                return True
            time.sleep(1)
        
        logger.warning(f"⚠️ {config['name']} 健康检查超时")
        return False
    
    def start_all_services(self):
        """启动所有服务"""
        logger.info("🎯 开始启动所有服务...")
        
        # 按顺序启动服务
        service_order = ['mcp_service', 'fastapi_backend', 'vue_frontend']
        
        for service_name in service_order:
            if not self.start_service(service_name):
                logger.error(f"❌ 服务启动失败: {service_name}")
                return False
            
            # 等待服务就绪
            time.sleep(3)  # 给服务一些启动时间
            
            if service_name != 'vue_frontend':  # Vue前端可能需要更长时间
                self.wait_for_service(service_name)
        
        logger.info("🎉 所有服务启动完成！")
        return True
    
    def show_system_info(self):
        """显示系统信息"""
        print("\n" + "="*60)
        print("🏥 家庭私人医生小帮手 v2.0.1")
        print("Vue.js + FastAPI 架构")
        print("="*60)
        print("\n📊 服务状态:")
        
        for service_name, service_info in self.processes.items():
            config = service_info['config']
            process = service_info['process']
            status = "运行中" if process.poll() is None else "已停止"
            health = "健康" if self.check_service_health(service_name) else "检查中"
            
            print(f"  • {config['name']}: {status} | {health} | 端口: {config['port']}")
        
        print("\n🌐 访问地址:")
        print("  • 前端界面: http://localhost:3000")
        print("  • 后端API: http://localhost:5001")
        print("  • MCP服务: http://localhost:8006")
        print("\n💡 使用说明:")
        print("  • 在浏览器中打开 http://localhost:3000 开始使用")
        print("  • 支持语音对话、文档上传、历史记录等功能")
        print("  • 按 Ctrl+C 优雅关闭系统")
        print("="*60)
    
    def shutdown(self):
        """关闭所有服务"""
        logger.info("🛑 开始关闭系统...")
        
        for service_name, service_info in self.processes.items():
            process = service_info['process']
            config = service_info['config']
            
            logger.info(f"🔄 关闭 {config['name']}...")
            
            try:
                process.terminate()
                try:
                    process.wait(timeout=5)
                    logger.info(f"✅ {config['name']} 已关闭")
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
                    logger.info(f"⚡ {config['name']} 已强制关闭")
            except Exception as e:
                logger.error(f"关闭 {config['name']} 时发生错误: {e}")
        
        logger.info("✅ 系统关闭完成")
    
    def run(self):
        """运行系统"""
        try:
            print("🏥 家庭私人医生小帮手 v2.0.1")
            print("正在启动 Vue.js + FastAPI 架构系统...")
            print()
            
            # 启动所有服务
            if not self.start_all_services():
                return False
            
            # 显示系统信息
            self.show_system_info()
            
            # 等待用户中断
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
            
            return True
            
        except Exception as e:
            logger.error(f"系统运行时发生错误: {e}")
            return False
        finally:
            self.shutdown()

def main():
    """主函数"""
    try:
        manager = SimpleSystemManager()
        success = manager.run()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
