#!/usr/bin/env python3
"""
启动聊天界面 - 快速解决方案
直接启动简单的HTML聊天界面
"""

import http.server
import socketserver
import webbrowser
import threading
import time
import os

def start_simple_server():
    """启动简单HTTP服务器"""
    PORT = 3001  # 使用不同端口避免冲突
    
    class SimpleHandler(http.server.SimpleHTTPRequestHandler):
        def do_GET(self):
            if self.path == '/' or self.path == '/index.html':
                self.path = '/simple_chat.html'
            
            # 添加CORS头
            super().do_GET()
        
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
    
    try:
        with socketserver.TCPServer(("", PORT), SimpleHandler) as httpd:
            print("🏥 家庭私人医生小帮手 - 聊天界面")
            print("=" * 50)
            print(f"🚀 服务器启动成功！")
            print(f"📍 访问地址: http://localhost:{PORT}")
            print(f"💬 聊天功能: http://localhost:{PORT}/simple_chat.html")
            print("=" * 50)
            print("✅ 聊天功能包括:")
            print("   📝 大文本输入框（底部）")
            print("   🟢 绿色发送按钮")
            print("   🤖 AI智能回复")
            print("   📊 实时连接状态")
            print("   ⌨️ Ctrl+Enter 快速发送")
            print("=" * 50)
            print("🌐 浏览器将自动打开，如未打开请手动访问上述地址")
            print("🛑 按 Ctrl+C 停止服务")
            print("=" * 50)
            
            httpd.serve_forever()
            
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ 端口 {PORT} 被占用，尝试端口 {PORT+1}")
            PORT += 1
            start_simple_server()
        else:
            print(f"❌ 启动失败: {e}")
    except KeyboardInterrupt:
        print("\n🛑 聊天服务已停止")

def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:3001')
    except:
        pass

def main():
    """主函数"""
    # 检查必要文件
    if not os.path.exists('simple_chat.html'):
        print("❌ 聊天界面文件不存在，请先运行完整安装")
        return
    
    # 启动浏览器线程
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动服务器
    start_simple_server()

if __name__ == "__main__":
    main()
