#!/usr/bin/env python3
"""
🏥 简化聊天功能测试
绕过可能有问题的组件，直接测试聊天API的基础功能

作者: TCM RAG System Team
版本: 2.0.1
"""

import requests
import json
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_simple_chat():
    """测试简化的聊天功能"""
    base_url = 'http://localhost:5001'
    
    logger.info("🧪 开始简化聊天功能测试...")
    
    # 1. 测试健康检查
    try:
        logger.info("1️⃣ 测试健康检查...")
        response = requests.get(f'{base_url}/api/health', timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            logger.info(f"✅ 健康检查通过: {health_data['status']}")
            logger.info(f"   组件状态: {health_data['components']}")
        else:
            logger.error(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ 健康检查异常: {e}")
        return False
    
    # 2. 测试会话创建
    try:
        logger.info("2️⃣ 测试会话创建...")
        response = requests.post(f'{base_url}/api/sessions', timeout=10)
        if response.status_code == 200:
            session_data = response.json()
            session_id = session_data['session_id']
            logger.info(f"✅ 会话创建成功: {session_id}")
        else:
            logger.error(f"❌ 会话创建失败: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ 会话创建异常: {e}")
        return False
    
    # 3. 测试简单聊天（使用较短的超时时间）
    try:
        logger.info("3️⃣ 测试聊天消息发送...")
        
        chat_payload = {
            'message': '你好',
            'session_id': session_id
        }
        
        logger.info("   发送消息: '你好'")
        start_time = time.time()
        
        response = requests.post(
            f'{base_url}/api/chat',
            json=chat_payload,
            timeout=15  # 较短的超时时间
        )
        
        elapsed_time = time.time() - start_time
        
        if response.status_code == 200:
            chat_data = response.json()
            logger.info(f"✅ 聊天消息发送成功")
            logger.info(f"   响应时间: {elapsed_time:.2f}秒")
            logger.info(f"   AI回复: {chat_data.get('response', '')[:100]}...")
            logger.info(f"   会话ID: {chat_data.get('session_id')}")
            return True
        else:
            logger.error(f"❌ 聊天消息发送失败: {response.status_code}")
            logger.error(f"   错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        logger.error("❌ 聊天请求超时（15秒）")
        logger.warning("   这可能表明DeepSeek API或其他组件存在问题")
        return False
    except Exception as e:
        logger.error(f"❌ 聊天测试异常: {e}")
        return False

def test_frontend_access():
    """测试前端访问"""
    logger.info("4️⃣ 测试前端访问...")
    try:
        response = requests.get('http://localhost:3000', timeout=10)
        if response.status_code == 200:
            logger.info("✅ 前端界面可正常访问")
            logger.info("   URL: http://localhost:3000")
            return True
        else:
            logger.error(f"❌ 前端访问失败: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ 前端访问异常: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始TCM聊天系统简化测试")
    logger.info("="*50)
    
    # 测试后端聊天功能
    chat_success = test_simple_chat()
    
    # 测试前端访问
    frontend_success = test_frontend_access()
    
    # 生成测试报告
    logger.info("="*50)
    logger.info("📊 测试结果汇总:")
    logger.info(f"   后端聊天功能: {'✅ 通过' if chat_success else '❌ 失败'}")
    logger.info(f"   前端界面访问: {'✅ 通过' if frontend_success else '❌ 失败'}")
    
    if chat_success and frontend_success:
        logger.info("🎉 聊天系统基础功能正常！")
        logger.info("💡 您可以在浏览器中访问 http://localhost:3000 使用聊天功能")
        return True
    else:
        logger.warning("⚠️ 聊天系统存在问题，需要进一步检查")
        if not chat_success:
            logger.warning("   - 后端聊天API可能存在超时或错误")
        if not frontend_success:
            logger.warning("   - 前端服务可能未正常启动")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
