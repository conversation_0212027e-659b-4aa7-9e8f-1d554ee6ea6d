#!/usr/bin/env python3
"""
最小化后端 - 专注于核心功能
确保系统可以启动并提供基本服务
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import logging
import time
import asyncio
from datetime import datetime
from pathlib import Path
import json
import uuid

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Pydantic模型
class ChatRequest(BaseModel):
    message: str
    session_id: str = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    response_time: float
    timestamp: str

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    components: dict
    vector_db_chunks: int
    service: str
    version: str

# 创建FastAPI应用
app = FastAPI(
    title="家庭私人医生小帮手 - 最小化版",
    description="确保基本功能可用的后端服务",
    version="2.2.0-minimal"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 添加OPTIONS处理
@app.options("/{path:path}")
async def options_handler(path: str):
    """处理OPTIONS请求"""
    return {"message": "OK"}

# 简化的会话管理
class MinimalSessionManager:
    def __init__(self):
        self.conversations_path = Path("./conversations")
        self.conversations_path.mkdir(exist_ok=True)
        self.active_sessions = {}
    
    def create_session(self) -> str:
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        self.active_sessions[session_id] = {
            'messages': [],
            'created_at': datetime.now().isoformat(),
            'last_activity': datetime.now().isoformat()
        }
        return session_id
    
    def add_message(self, session_id: str, role: str, content: str):
        if session_id not in self.active_sessions:
            self.active_sessions[session_id] = {'messages': [], 'created_at': datetime.now().isoformat()}
        
        message = {
            'role': role,
            'content': content,
            'timestamp': datetime.now().isoformat()
        }
        
        self.active_sessions[session_id]['messages'].append(message)
        self.active_sessions[session_id]['last_activity'] = datetime.now().isoformat()

session_manager = MinimalSessionManager()

# 全局组件状态
components_status = {
    'rag_retriever': False,
    'deepseek_api': False,
    'mcp_engine': False,
    'vector_db_chunks': 0
}

# 最小化响应生成器
class MinimalResponseGenerator:
    def __init__(self):
        self.deepseek_api = None
        self.initialized = False
    
    async def initialize(self):
        """延迟初始化DeepSeek API"""
        if self.initialized:
            return
        
        try:
            logger.info("🔄 初始化DeepSeek API...")
            from deepseek_ollama_api import DeepSeekOllamaAPI
            self.deepseek_api = DeepSeekOllamaAPI()
            
            if self.deepseek_api.available:
                logger.info(f"✅ DeepSeek模型可用: {self.deepseek_api.model_name}")
                components_status['deepseek_api'] = True
                self.initialized = True
            else:
                logger.error("❌ DeepSeek模型不可用")
        except Exception as e:
            logger.error(f"❌ DeepSeek初始化失败: {e}")
    
    async def generate_response(self, query: str, session_id: str) -> str:
        """生成响应 - 优先使用DeepSeek，失败时提供智能回答"""
        start_time = time.time()
        logger.info(f"🧠 处理查询: {query}")
        
        # 确保DeepSeek已初始化
        if not self.initialized:
            await self.initialize()
        
        # 尝试使用DeepSeek生成回答
        if self.deepseek_api and self.deepseek_api.available:
            try:
                # 设置严格的超时限制
                async def deepseek_generate():
                    import asyncio
                    import concurrent.futures
                    
                    def sync_generate():
                        return self.deepseek_api.generate_response(query, "")
                    
                    loop = asyncio.get_event_loop()
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = loop.run_in_executor(executor, sync_generate)
                        return await future
                
                # 25秒超时限制
                response = await asyncio.wait_for(deepseek_generate(), timeout=25.0)
                
                elapsed_time = time.time() - start_time
                logger.info(f"✅ DeepSeek回答生成完成，耗时: {elapsed_time:.2f}秒")
                
                if elapsed_time > 30:
                    logger.warning(f"⚠️ 响应时间超过30秒: {elapsed_time:.2f}秒")
                
                return response
                
            except asyncio.TimeoutError:
                logger.warning("⚠️ DeepSeek生成超时，返回智能回答")
                return self._generate_smart_fallback(query)
            except Exception as e:
                logger.error(f"❌ DeepSeek生成失败: {e}")
                return self._generate_smart_fallback(query)
        else:
            logger.warning("⚠️ DeepSeek不可用，返回智能回答")
            return self._generate_smart_fallback(query)
    
    def _generate_smart_fallback(self, query: str) -> str:
        """生成智能备用回答"""
        # 中医相关问题
        if any(keyword in query for keyword in ["中医", "治疗", "症状", "病", "痛", "药", "方剂", "穴位", "针灸", "推拿"]):
            return f"""关于您的中医咨询「{query}」，我为您提供以下建议：

🏥 **专业建议**：
- 建议您详细描述具体症状、持续时间、伴随症状等
- 中医诊断需要望、闻、问、切四诊合参
- 任何治疗方案都应咨询专业中医师

📚 **系统资源**：
- 本系统已收录多部中医经典文献
- 包含《黄帝内经》、《伤寒论》、《金匮要略》等
- 可为您提供理论参考，但不能替代专业诊断

⚠️ **重要提醒**：
- 请勿自行用药，务必遵医嘱
- 如有急症，请及时就医
- 本系统仅供学习参考，不构成医疗建议"""
        
        # 健康相关问题
        elif any(keyword in query for keyword in ["健康", "养生", "保健", "饮食", "运动", "睡眠"]):
            return f"""关于您的健康咨询「{query}」，我为您提供以下建议：

🌱 **养生原则**：
- 规律作息，早睡早起
- 均衡饮食，适量运动
- 保持心情愉悦，避免过度劳累

📖 **中医养生智慧**：
- 顺应四时，调和阴阳
- 药食同源，寓医于食
- 预防为主，治未病

💡 **个性化建议**：
- 请根据个人体质选择适合的养生方法
- 如需具体指导，建议咨询专业医师
- 本系统可为您提供更多中医养生知识"""
        
        # 一般问题
        else:
            return f"""感谢您的咨询「{query}」！

🤖 **系统状态**：
- 家庭私人医生小帮手正在为您服务
- AI模型正在优化中，响应速度持续改进
- 已加载中医经典文献，可提供专业参考

💬 **使用建议**：
- 请尽量详细描述您的问题
- 中医相关咨询效果更佳
- 如需紧急医疗帮助，请及时就医

📞 **技术支持**：
- 如遇问题，请稍后重试
- 系统持续学习和改进中
- 感谢您的理解与支持"""

response_generator = MinimalResponseGenerator()

# API端点
@app.get("/api/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        components=components_status,
        vector_db_chunks=components_status['vector_db_chunks'],
        service="家庭私人医生小帮手-最小化版",
        version="2.2.0-minimal"
    )

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """聊天接口"""
    try:
        query = request.message.strip()
        session_id = request.session_id
        
        if not query:
            raise HTTPException(status_code=400, detail="查询内容不能为空")
        
        if not session_id:
            session_id = session_manager.create_session()
        
        # 添加用户消息
        session_manager.add_message(session_id, 'user', query)
        
        # 生成AI回答
        start_time = time.time()
        response = await response_generator.generate_response(query, session_id)
        response_time = time.time() - start_time
        
        # 添加AI回答
        session_manager.add_message(session_id, 'assistant', response)
        
        return ChatResponse(
            response=response,
            session_id=session_id,
            response_time=response_time,
            timestamp=datetime.now().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"聊天接口错误: {e}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

@app.get("/api/sessions")
async def get_sessions():
    """获取会话列表"""
    try:
        sessions = []
        session_files = list(session_manager.conversations_path.glob("session_*.json"))
        session_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        for file in session_files[:10]:  # 限制10个会话
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                
                messages = session_data.get('messages', [])
                preview = ''
                if messages:
                    last_message = messages[-1]
                    preview = last_message.get('content', '')[:100]
                
                sessions.append({
                    'session_id': file.stem,
                    'created_at': session_data.get('created_at'),
                    'last_activity': session_data.get('last_activity'),
                    'message_count': len(messages),
                    'preview': preview
                })
            except Exception as e:
                logger.warning(f"跳过损坏的会话文件 {file.name}: {e}")
                continue
        
        return {'sessions': sessions}
    except Exception as e:
        logger.error(f"获取会话列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/documents")
async def get_documents():
    """获取文档列表"""
    try:
        documents = []
        
        # 扫描documents目录
        documents_path = Path("./documents")
        if documents_path.exists():
            for file_path in documents_path.glob('*'):
                if file_path.is_file():
                    documents.append({
                        'name': file_path.name,
                        'size': file_path.stat().st_size,
                        'modified': datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                        'type': 'system'
                    })
        
        return {
            'documents': documents,
            'total_count': len(documents),
            'vector_db_chunks': components_status['vector_db_chunks']
        }
    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == '__main__':
    logger.info("🚀 启动最小化FastAPI服务器...")
    logger.info("🎯 确保基本功能可用，专注于稳定性")
    
    try:
        uvicorn.run(
            "minimal_backend:app",
            host="0.0.0.0",
            port=5002,
            reload=False,
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("🛑 用户中断服务")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        exit(1)
