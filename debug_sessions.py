#!/usr/bin/env python3
"""
调试会话API问题
"""

import requests
import json

def debug_sessions_api():
    """调试会话API的详细错误"""
    print("🔍 调试会话API...")
    
    # 先创建一个会话
    print("\n1. 创建测试会话...")
    try:
        response = requests.post('http://localhost:5001/api/sessions', timeout=10)
        if response.status_code == 200:
            session_data = response.json()
            session_id = session_data['session_id']
            print(f"   创建成功: {session_id}")
            
            # 向会话添加一条消息
            print("\n2. 向会话添加消息...")
            chat_response = requests.post('http://localhost:5001/api/chat', 
                                        json={'message': '测试消息', 'session_id': session_id}, 
                                        timeout=30)
            if chat_response.status_code == 200:
                print("   消息添加成功")
            else:
                print(f"   消息添加失败: {chat_response.text}")
        else:
            print(f"   创建失败: {response.text}")
            return
    except Exception as e:
        print(f"   异常: {e}")
        return
    
    # 测试获取会话列表
    print("\n3. 获取会话列表...")
    try:
        response = requests.get('http://localhost:5001/api/sessions', timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   会话数量: {len(data.get('sessions', []))}")
            for session in data.get('sessions', [])[:3]:  # 只显示前3个
                print(f"     - {session.get('session_id')}: {session.get('message_count')} 条消息")
        else:
            print(f"   错误详情: {response.text}")
            
    except Exception as e:
        print(f"   异常: {e}")
        import traceback
        traceback.print_exc()

def check_session_files():
    """检查会话文件"""
    print("\n🔍 检查会话文件...")
    
    from pathlib import Path
    
    conversations_path = Path("conversations")
    if conversations_path.exists():
        session_files = list(conversations_path.glob("session_*.json"))
        print(f"   找到 {len(session_files)} 个会话文件")
        
        for file in session_files[:3]:  # 只检查前3个
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"   {file.name}: {len(data.get('messages', []))} 条消息")
            except Exception as e:
                print(f"   {file.name}: 读取失败 - {e}")
    else:
        print("   conversations目录不存在")

def main():
    print("🏥 会话API调试工具")
    print("=" * 50)
    
    debug_sessions_api()
    check_session_files()
    
    print("\n" + "=" * 50)
    print("调试完成")

if __name__ == "__main__":
    main()
