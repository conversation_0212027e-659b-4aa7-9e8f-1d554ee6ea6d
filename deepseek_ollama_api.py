#!/usr/bin/env python3
"""
DeepSeek Ollama API接口
通过Ollama调用本地DeepSeek-R1模型
"""

import requests
import json
import logging
from typing import Dict, List, Optional, Generator
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DeepSeekOllamaAPI:
    """DeepSeek Ollama API管理器"""
    
    def __init__(self, base_url: str = "http://localhost:11434", model_name: str = "deepseek-r1-q4km:latest"):
        self.base_url = base_url
        self.model_name = model_name

        # 优化会话配置
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Connection': 'keep-alive'
        })

        # 设置连接池
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=1,
            pool_maxsize=1,
            max_retries=2
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)

        self.available = False
        self.check_availability()
    
    def check_availability(self) -> bool:
        """检查Ollama服务和模型可用性"""
        try:
            # 检查Ollama服务
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model['name'] for model in models]
                
                if self.model_name in model_names:
                    self.available = True
                    logger.info(f"✅ DeepSeek模型 {self.model_name} 可用")
                    return True
                else:
                    logger.warning(f"⚠️ 模型 {self.model_name} 不存在，可用模型: {model_names}")
                    # 尝试使用第一个DeepSeek模型
                    for name in model_names:
                        if 'deepseek' in name.lower():
                            self.model_name = name
                            self.available = True
                            logger.info(f"✅ 使用替代模型: {self.model_name}")
                            return True
            else:
                logger.error(f"❌ Ollama服务响应错误: {response.status_code}")
        except Exception as e:
            logger.error(f"❌ Ollama服务检查失败: {e}")
        
        self.available = False
        return False
    
    def generate_response(self, prompt: str, context: str = "", max_tokens: int = 256, temperature: float = 0.7) -> str:
        """生成回答"""
        if not self.available:
            return "DeepSeek模型服务不可用，请检查Ollama服务状态。"

        try:
            # 构建完整的提示词
            full_prompt = self._build_prompt(prompt, context)

            # 优化的请求参数 - 提高响应速度
            request_data = {
                "model": self.model_name,
                "prompt": full_prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "num_predict": 150,   # 减少生成长度以提高速度
                    "top_p": 0.8,         # 降低采样范围
                    "top_k": 20,          # 减少候选词数量
                    "repeat_penalty": 1.05, # 降低重复惩罚
                    "num_ctx": 1024,      # 进一步限制上下文长度
                    "num_thread": 8,      # 增加线程数
                    "num_gpu": 1,         # 启用GPU加速
                    "num_batch": 256,     # 减少批处理大小
                    "stop": ["\n\n", "问题：", "回答："]  # 添加停止词
                }
            }

            # 调用Ollama API - 带重试机制，优化超时
            max_retries = 2
            base_timeout = 15  # 减少基础超时时间

            for attempt in range(max_retries + 1):
                try:
                    # 递增超时时间
                    timeout = base_timeout + (attempt * 10)

                    response = self.session.post(
                        f"{self.base_url}/api/generate",
                        json=request_data,
                        timeout=timeout
                    )
                    break  # 成功则跳出重试循环

                except requests.exceptions.Timeout as e:
                    if attempt < max_retries:
                        logger.warning(f"⚠️ DeepSeek请求超时 ({timeout}s)，重试 {attempt + 1}/{max_retries}")
                        time.sleep(2)  # 增加等待时间
                        continue
                    else:
                        logger.error(f"❌ DeepSeek请求最终超时，已重试 {max_retries} 次")
                        raise e

                except requests.exceptions.RequestException as e:
                    if attempt < max_retries:
                        logger.warning(f"⚠️ DeepSeek请求异常，重试 {attempt + 1}/{max_retries}: {e}")
                        time.sleep(2)
                        continue
                    else:
                        logger.error(f"❌ DeepSeek请求最终失败: {e}")
                        raise e
            
            if response.status_code == 200:
                result = response.json()
                generated_text = result.get('response', '').strip()
                
                if generated_text:
                    logger.info(f"✅ DeepSeek生成回答成功，长度: {len(generated_text)}")
                    return self._post_process_response(generated_text)
                else:
                    logger.warning("⚠️ DeepSeek返回空回答")
                    return "抱歉，模型未能生成有效回答，请重新提问。"
            else:
                logger.error(f"❌ Ollama API错误: {response.status_code}")
                return "模型服务暂时不可用，请稍后重试。"
                
        except Exception as e:
            logger.error(f"❌ DeepSeek生成失败: {e}")
            return "生成回答时发生错误，请重新尝试。"
    
    def _build_prompt(self, query: str, context: str) -> str:
        """构建提示词"""
        system_prompt = """你是专业中医助手。基于中医理论和检索资料，提供准确实用的建议。要求：专业准确、通俗易懂、提醒咨询医师。"""

        # 限制上下文长度
        if context.strip():
            context = context[:800]  # 限制上下文长度
            prompt = f"""{system_prompt}

资料：{context}

问题：{query}

回答："""
        else:
            prompt = f"""{system_prompt}

问题：{query}

回答："""

        return prompt
    
    def _post_process_response(self, response: str) -> str:
        """后处理回答"""
        # 清理回答
        response = response.strip()
        
        # 确保回答以适当的格式开始
        if not response.startswith(('##', '**', '根据', '基于')):
            response = f"## 🏥 中医专业建议\n\n{response}"
        
        # 添加免责声明（如果没有的话）
        if "咨询专业中医师" not in response and "医师指导" not in response:
            response += "\n\n**⚠️ 重要提醒**: 以上建议仅供参考，具体诊疗请咨询专业中医师。"
        
        return response
    
    def test_model(self) -> bool:
        """测试模型功能"""
        if not self.available:
            return False
        
        try:
            test_prompt = "请简单介绍一下中医的基本理论。"
            response = self.generate_response(test_prompt)
            
            if response and len(response) > 50:
                logger.info("✅ DeepSeek模型测试成功")
                return True
            else:
                logger.warning("⚠️ DeepSeek模型测试失败")
                return False
        except Exception as e:
            logger.error(f"❌ DeepSeek模型测试异常: {e}")
            return False

# 全局实例
deepseek_api = DeepSeekOllamaAPI()

def get_deepseek_response(query: str, context: str = "") -> str:
    """获取DeepSeek回答的便捷函数"""
    return deepseek_api.generate_response(query, context)

if __name__ == "__main__":
    # 测试API
    print("🧪 测试DeepSeek Ollama API...")
    
    if deepseek_api.check_availability():
        print("✅ 服务可用，开始测试...")
        
        test_queries = [
            "什么是中医的阴阳学说？",
            "失眠多梦怎么办？"
        ]
        
        for query in test_queries:
            print(f"\n🔍 测试查询: {query}")
            response = deepseek_api.generate_response(query)
            print(f"📝 回答: {response[:200]}...")
    else:
        print("❌ 服务不可用")
