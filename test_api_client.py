#!/usr/bin/env python3
"""
API测试客户端 - 用于测试FastAPI接口
"""

import requests
import json
import time

def test_health_endpoint(base_url):
    """测试健康检查端点"""
    try:
        print(f"🔍 测试健康检查: {base_url}/api/health")
        response = requests.get(f"{base_url}/api/health", timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def test_chat_endpoint(base_url):
    """测试聊天端点"""
    try:
        print(f"🔍 测试聊天接口: {base_url}/api/chat")
        
        data = {
            "message": "你好",
            "session_id": "test-session-123"
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        print(f"发送数据: {json.dumps(data, ensure_ascii=False)}")
        
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/chat", 
            json=data, 
            headers=headers, 
            timeout=30
        )
        elapsed_time = time.time() - start_time
        
        print(f"状态码: {response.status_code}")
        print(f"响应时间: {elapsed_time:.2f}秒")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        return response.status_code == 200
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 聊天接口测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始API接口测试...")
    
    # 测试不同端口的服务
    test_urls = [
        "http://localhost:5001",  # 原始FastAPI
        "http://localhost:5002",  # 测试FastAPI
        "http://localhost:5003",  # uvicorn FastAPI
    ]
    
    for base_url in test_urls:
        print(f"\n{'='*50}")
        print(f"测试服务: {base_url}")
        print(f"{'='*50}")
        
        # 测试健康检查
        health_ok = test_health_endpoint(base_url)
        
        if health_ok:
            print("✅ 健康检查通过")
            # 测试聊天功能
            chat_ok = test_chat_endpoint(base_url)
            if chat_ok:
                print("✅ 聊天接口测试通过")
            else:
                print("❌ 聊天接口测试失败")
        else:
            print("❌ 健康检查失败，跳过聊天测试")
        
        time.sleep(1)  # 避免请求过快

if __name__ == "__main__":
    main()
