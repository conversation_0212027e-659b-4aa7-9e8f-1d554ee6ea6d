#!/usr/bin/env python3
"""
简化版FastAPI测试 - 用于诊断问题
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import logging
import time
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 请求模型
class ChatRequest(BaseModel):
    message: str
    session_id: str = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    response_time: float
    timestamp: str

# 创建FastAPI应用
app = FastAPI(
    title="测试版家庭私人医生小帮手",
    description="简化版FastAPI测试",
    version="2.0.0-test"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/api/health")
async def health_check():
    """系统健康检查"""
    return {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': '测试版家庭私人医生小帮手',
        'version': '2.0.0-test'
    }

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """简化版聊天接口"""
    try:
        query = request.message.strip()
        session_id = request.session_id or "test-session"
        
        if not query:
            raise HTTPException(status_code=400, detail="查询内容不能为空")
        
        start_time = time.time()
        
        # 简单的回答生成
        response = f"您好！我收到了您的问题：{query}。这是一个测试回答。"
        
        response_time = time.time() - start_time
        
        logger.info(f"处理查询: {query}, 耗时: {response_time:.2f}秒")
        
        return ChatResponse(
            response=response,
            session_id=session_id,
            response_time=response_time,
            timestamp=datetime.now().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"聊天接口错误: {e}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    logger.info("🚀 启动测试版FastAPI服务器...")
    uvicorn.run(app, host="0.0.0.0", port=5002, log_level="info")
