import axios from 'axios'

// API基础配置 - 更新为FastAPI端口
const API_BASE = process.env.VUE_APP_API_BASE || 'http://localhost:5002'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE,
  timeout: 45000, // 45秒超时，适应DeepSeek模型响应时间
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API请求: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API响应: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('API响应错误:', error.response?.status, error.response?.data || error.message)
    
    // 统一错误处理
    if (error.response) {
      const { status, data } = error.response
      let errorMessage = data?.error || data?.message || '服务器错误'
      
      switch (status) {
        case 400:
          errorMessage = `请求错误: ${errorMessage}`
          break
        case 401:
          errorMessage = '未授权访问'
          break
        case 403:
          errorMessage = '访问被禁止'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 408:
          errorMessage = '请求超时'
          break
        case 500:
          errorMessage = `服务器内部错误: ${errorMessage}`
          break
        case 503:
          errorMessage = '服务暂时不可用'
          break
        default:
          errorMessage = `网络错误 (${status}): ${errorMessage}`
      }
      
      error.message = errorMessage
    } else if (error.request) {
      error.message = '网络连接失败，请检查网络设置'
    } else {
      error.message = error.message || '未知错误'
    }
    
    return Promise.reject(error)
  }
)

// API服务类
class ApiService {
  // ==================== 系统健康检查 ====================
  
  async checkHealth() {
    return apiClient.get('/api/health')
  }

  // ==================== 聊天相关API ====================
  
  async sendMessage(message, sessionId = null) {
    return apiClient.post('/api/chat', {
      message,
      session_id: sessionId
    })
  }

  // 创建SSE连接进行流式聊天
  createChatStream(message, sessionId = null) {
    return new Promise((resolve, reject) => {
      const eventSource = new EventSource(
        `${API_BASE}/api/chat/stream`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            message,
            session_id: sessionId
          })
        }
      )

      const streamData = {
        sessionId: null,
        messages: [],
        isComplete: false,
        error: null
      }

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          
          switch (data.type) {
            case 'start':
              streamData.sessionId = data.session_id
              break
            case 'user_message':
              streamData.messages.push({
                role: 'user',
                content: data.content,
                timestamp: new Date().toISOString()
              })
              break
            case 'processing':
              streamData.messages.push({
                role: 'system',
                content: data.message,
                timestamp: new Date().toISOString()
              })
              break
            case 'response':
              streamData.messages.push({
                role: 'assistant',
                content: data.content,
                timestamp: new Date().toISOString()
              })
              break
            case 'complete':
              streamData.isComplete = true
              eventSource.close()
              resolve(streamData)
              break
            case 'error':
              streamData.error = data.message
              eventSource.close()
              reject(new Error(data.message))
              break
          }
        } catch (error) {
          console.error('SSE数据解析错误:', error)
        }
      }

      eventSource.onerror = (error) => {
        console.error('SSE连接错误:', error)
        eventSource.close()
        reject(new Error('流式连接失败'))
      }

      // 返回可以取消的对象
      return {
        cancel: () => eventSource.close(),
        stream: streamData
      }
    })
  }

  // ==================== 会话管理API ====================
  
  async getSessions() {
    return apiClient.get('/api/sessions')
  }

  async getSession(sessionId) {
    return apiClient.get(`/api/sessions/${sessionId}`)
  }

  async createSession() {
    return apiClient.post('/api/sessions')
  }

  async deleteSession(sessionId) {
    return apiClient.delete(`/api/sessions/${sessionId}`)
  }

  // ==================== 语音功能API ====================
  
  async speakText(text) {
    return apiClient.post('/api/voice/speak', { text })
  }

  async recognizeVoice(timeout = 10, phraseTimeLimit = 15) {
    return apiClient.post('/api/voice/recognize', {
      timeout,
      phrase_time_limit: phraseTimeLimit
    })
  }

  // ==================== 文档管理API ====================
  
  async uploadDocuments(files, onProgress = null) {
    const formData = new FormData()
    
    // 添加文件到FormData
    for (let i = 0; i < files.length; i++) {
      formData.append('files', files[i])
    }

    return apiClient.post('/api/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(percentCompleted)
        }
      }
    })
  }

  async getDocuments() {
    return apiClient.get('/api/documents')
  }

  // ==================== 工具方法 ====================
  
  // 检查API连接状态
  async testConnection() {
    try {
      const response = await this.checkHealth()
      return {
        connected: true,
        status: response.data.status,
        latency: Date.now() - response.config.metadata?.startTime || 0
      }
    } catch (error) {
      return {
        connected: false,
        error: error.message,
        latency: -1
      }
    }
  }

  // 获取API基础URL
  getApiBase() {
    return API_BASE
  }
}

// 创建并导出API服务实例
const apiService = new ApiService()

export default apiService

// 同时导出类，以便需要时创建新实例
export { ApiService }
