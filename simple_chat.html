<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 家庭私人医生小帮手 - 智能聊天</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
        }
        
        .chat-container {
            height: 70vh;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            background: white;
            padding: 1rem;
        }
        
        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
            max-width: 80%;
        }
        
        .message-user {
            background-color: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        
        .message-assistant {
            background-color: #e9ecef;
            color: #333;
            margin-right: auto;
        }
        
        .typing-indicator {
            display: none;
            padding: 1rem;
            text-align: center;
            color: #6c757d;
        }
        
        .input-area {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 1rem;
            border-top: 1px solid #dee2e6;
        }
        
        #messageInput {
            resize: none;
            min-height: 60px;
        }
        
        .navbar-brand {
            font-weight: bold;
        }
        
        .welcome-message {
            text-align: center;
            color: #6c757d;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="#" onclick="location.reload()">
                <i class="bi bi-heart-pulse-fill me-2"></i>
                <span>🏥 家庭私人医生小帮手</span>
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-light">
                    <i class="bi bi-chat-dots me-1"></i>智能咨询
                </span>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-xl-6">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-robot me-2"></i>
                            <h5 class="mb-0">AI医生智能咨询</h5>
                            <div class="ms-auto">
                                <span class="badge bg-light text-dark" id="statusBadge">
                                    <i class="bi bi-circle-fill text-success"></i> 在线
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 聊天消息区域 -->
                    <div class="chat-container" id="chatContainer">
                        <div class="welcome-message">
                            <i class="bi bi-chat-heart display-4 text-success mb-3"></i>
                            <h4>欢迎使用AI医生咨询</h4>
                            <p>请在下方输入您的健康问题，我将为您提供专业的中医建议</p>
                        </div>
                    </div>
                    
                    <!-- 加载指示器 -->
                    <div class="typing-indicator" id="typingIndicator">
                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        AI医生正在思考中...
                    </div>
                    
                    <!-- 输入区域 -->
                    <div class="input-area">
                        <div class="row g-2">
                            <div class="col">
                                <div class="input-group">
                                    <textarea
                                        id="messageInput"
                                        class="form-control"
                                        placeholder="请输入您的健康问题，例如：失眠、头痛、消化不良等..."
                                        rows="2"
                                        style="resize: none;"
                                    ></textarea>
                                </div>
                            </div>
                            <div class="col-auto">
                                <button 
                                    id="sendButton"
                                    class="btn btn-success btn-lg h-100"
                                    onclick="sendMessage()"
                                >
                                    <i class="bi bi-send-fill"></i>
                                    <span class="d-none d-sm-inline ms-1">发送</span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 输入提示 -->
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="bi bi-lightbulb me-1"></i>
                                按 Ctrl+Enter 快速发送 | 支持中医症状咨询和方剂查询
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 全局变量
        let currentSessionId = null;
        const API_BASE_URL = 'http://localhost:5002';
        
        // DOM元素
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const typingIndicator = document.getElementById('typingIndicator');
        const statusBadge = document.getElementById('statusBadge');
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            messageInput.addEventListener('keydown', handleKeyDown);
            messageInput.addEventListener('input', adjustTextareaHeight);
            
            // 检查后端连接
            checkBackendStatus();
        });
        
        // 检查后端状态
        async function checkBackendStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/health`);
                if (response.ok) {
                    statusBadge.innerHTML = '<i class="bi bi-circle-fill text-success"></i> 在线';
                } else {
                    statusBadge.innerHTML = '<i class="bi bi-circle-fill text-warning"></i> 连接异常';
                }
            } catch (error) {
                statusBadge.innerHTML = '<i class="bi bi-circle-fill text-danger"></i> 离线';
                console.error('后端连接检查失败:', error);
            }
        }
        
        // 处理键盘事件
        function handleKeyDown(event) {
            if (event.ctrlKey && event.key === 'Enter') {
                event.preventDefault();
                sendMessage();
            }
        }
        
        // 自动调整文本框高度
        function adjustTextareaHeight() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
        }
        
        // 发送消息
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            // 禁用输入
            messageInput.disabled = true;
            sendButton.disabled = true;
            
            // 添加用户消息
            addMessage('user', message);
            
            // 清空输入框
            messageInput.value = '';
            adjustTextareaHeight();
            
            // 显示加载指示器
            showTypingIndicator();
            
            try {
                // 发送到API
                const response = await fetch(`${API_BASE_URL}/api/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: currentSessionId
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // 更新会话ID
                    if (data.session_id) {
                        currentSessionId = data.session_id;
                    }
                    
                    // 添加AI回复
                    addMessage('assistant', data.response);
                    
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('system', `发送失败: ${error.message}`);
            } finally {
                // 恢复输入
                messageInput.disabled = false;
                sendButton.disabled = false;
                hideTypingIndicator();
                messageInput.focus();
            }
        }
        
        // 添加消息到聊天区域
        function addMessage(role, content) {
            // 移除欢迎消息
            const welcomeMessage = chatContainer.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${role}`;
            
            if (role === 'user') {
                messageDiv.innerHTML = `
                    <div class="d-flex align-items-start">
                        <div class="flex-grow-1">${content}</div>
                        <i class="bi bi-person-circle ms-2"></i>
                    </div>
                `;
            } else if (role === 'assistant') {
                messageDiv.innerHTML = `
                    <div class="d-flex align-items-start">
                        <i class="bi bi-robot me-2"></i>
                        <div class="flex-grow-1">${formatAIResponse(content)}</div>
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="text-center text-danger">
                        <i class="bi bi-exclamation-triangle me-1"></i>
                        ${content}
                    </div>
                `;
                messageDiv.className = 'message text-center';
            }
            
            chatContainer.appendChild(messageDiv);
            scrollToBottom();
        }
        
        // 格式化AI回复
        function formatAIResponse(content) {
            // 简单的Markdown格式化
            return content
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/\n/g, '<br>');
        }
        
        // 显示加载指示器
        function showTypingIndicator() {
            typingIndicator.style.display = 'block';
            scrollToBottom();
        }
        
        // 隐藏加载指示器
        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }
        
        // 滚动到底部
        function scrollToBottom() {
            setTimeout(() => {
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }, 100);
        }
    </script>
</body>
</html>
