#!/usr/bin/env python3
"""
🏥 后端服务修复脚本
专门用于修复FastAPI后端服务的问题

作者: TCM RAG System Team
版本: 2.0.1
"""

import os
import sys
import time
import subprocess
import logging
import signal
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def kill_python_processes():
    """终止所有Python进程"""
    logger.info("🔄 清理现有Python进程...")
    try:
        # 获取所有Python进程
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                              capture_output=True, text=True, shell=True)
        
        if 'python.exe' in result.stdout:
            logger.info("发现Python进程，正在终止...")
            subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                         capture_output=True, shell=True)
            time.sleep(2)
        
        # 检查pythonw.exe
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq pythonw.exe'], 
                              capture_output=True, text=True, shell=True)
        
        if 'pythonw.exe' in result.stdout:
            logger.info("发现Pythonw进程，正在终止...")
            subprocess.run(['taskkill', '/F', '/IM', 'pythonw.exe'], 
                         capture_output=True, shell=True)
            time.sleep(2)
            
        logger.info("✅ Python进程清理完成")
        
    except Exception as e:
        logger.warning(f"清理进程时发生错误: {e}")

def check_port_availability(port):
    """检查端口是否可用"""
    try:
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True, shell=True)
        for line in result.stdout.split('\n'):
            if f':{port}' in line and 'LISTENING' in line:
                return False
        return True
    except:
        return True

def start_backend_only():
    """只启动后端服务"""
    logger.info("🚀 启动FastAPI后端服务...")
    
    # 检查端口5001
    if not check_port_availability(5001):
        logger.warning("端口5001被占用，尝试清理...")
        kill_python_processes()
        time.sleep(3)
    
    try:
        # 启动FastAPI后端
        process = subprocess.Popen(
            [sys.executable, 'fastapi_backend.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        logger.info(f"✅ FastAPI后端启动成功 (PID: {process.pid})")
        
        # 等待服务启动
        logger.info("⏳ 等待服务就绪...")
        for i in range(15):
            try:
                import requests
                response = requests.get('http://localhost:5001/api/health', timeout=5)
                if response.status_code == 200:
                    logger.info("✅ FastAPI后端服务就绪")
                    return process
            except:
                pass
            time.sleep(2)
        
        logger.warning("⚠️ FastAPI后端健康检查超时，但进程已启动")
        return process
        
    except Exception as e:
        logger.error(f"❌ 启动FastAPI后端失败: {e}")
        return None

def start_mcp_service():
    """启动MCP服务"""
    logger.info("🤖 启动MCP智能服务...")
    
    # 检查端口8006
    if not check_port_availability(8006):
        logger.warning("端口8006被占用，跳过MCP服务启动")
        return None
    
    try:
        process = subprocess.Popen(
            [sys.executable, 'intelligent_mcp_service.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        logger.info(f"✅ MCP服务启动成功 (PID: {process.pid})")
        
        # 等待服务启动
        time.sleep(3)
        try:
            import requests
            response = requests.get('http://localhost:8006/health', timeout=5)
            if response.status_code == 200:
                logger.info("✅ MCP服务就绪")
        except:
            logger.warning("⚠️ MCP服务健康检查失败，但进程已启动")
        
        return process
        
    except Exception as e:
        logger.error(f"❌ 启动MCP服务失败: {e}")
        return None

def test_backend():
    """测试后端功能"""
    logger.info("🧪 测试后端功能...")
    
    try:
        import requests
        
        # 测试健康检查
        response = requests.get('http://localhost:5001/api/health', timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            logger.info(f"✅ 健康检查通过: {health_data['status']}")
            
            # 测试简单聊天
            chat_payload = {'message': '你好', 'session_id': None}
            response = requests.post('http://localhost:5001/api/chat', 
                                   json=chat_payload, timeout=15)
            
            if response.status_code == 200:
                chat_data = response.json()
                logger.info("✅ 聊天功能测试通过")
                logger.info(f"   AI回复: {chat_data.get('response', '')[:50]}...")
                return True
            else:
                logger.error(f"❌ 聊天功能测试失败: {response.status_code}")
                return False
        else:
            logger.error(f"❌ 健康检查失败: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 后端测试异常: {e}")
        return False

def main():
    """主函数"""
    logger.info("🔧 开始修复TCM后端服务")
    logger.info("="*50)
    
    # 1. 清理现有进程
    kill_python_processes()
    
    # 2. 启动MCP服务
    mcp_process = start_mcp_service()
    
    # 3. 启动后端服务
    backend_process = start_backend_only()
    
    if not backend_process:
        logger.error("❌ 后端服务启动失败")
        return False
    
    # 4. 测试后端功能
    time.sleep(5)  # 给服务更多启动时间
    success = test_backend()
    
    # 5. 显示结果
    logger.info("="*50)
    logger.info("📊 修复结果:")
    logger.info(f"   MCP服务: {'✅ 运行中' if mcp_process else '❌ 未启动'}")
    logger.info(f"   FastAPI后端: {'✅ 运行中' if backend_process else '❌ 未启动'}")
    logger.info(f"   后端功能测试: {'✅ 通过' if success else '❌ 失败'}")
    
    if success:
        logger.info("🎉 后端服务修复成功！")
        logger.info("💡 现在可以正常使用聊天功能了")
        
        # 保持服务运行
        try:
            logger.info("按 Ctrl+C 停止服务...")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 正在停止服务...")
            if backend_process:
                backend_process.terminate()
            if mcp_process:
                mcp_process.terminate()
    else:
        logger.error("❌ 后端服务修复失败")
        return False
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
