#!/usr/bin/env python3
"""
优化版后端 - 专注于性能和稳定性
解决DeepSeek API性能问题，确保响应时间<30秒
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import logging
import time
import asyncio
import concurrent.futures
from datetime import datetime
from pathlib import Path
import json
import uuid

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 全局组件
rag_retriever = None
deepseek_api = None
mcp_engine = None

# Pydantic模型
class ChatRequest(BaseModel):
    message: str
    session_id: str = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    response_time: float
    timestamp: str

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    components: dict
    vector_db_chunks: int
    service: str
    version: str

# 创建FastAPI应用
app = FastAPI(
    title="家庭私人医生小帮手 - 优化版",
    description="专注于性能优化的后端服务",
    version="2.1.0-optimized"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 会话管理
class SimpleSessionManager:
    def __init__(self):
        self.conversations_path = Path("./conversations")
        self.conversations_path.mkdir(exist_ok=True)
        self.active_sessions = {}
    
    def create_session(self) -> str:
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        self.active_sessions[session_id] = {
            'messages': [],
            'created_at': datetime.now().isoformat(),
            'last_activity': datetime.now().isoformat()
        }
        return session_id
    
    def add_message(self, session_id: str, role: str, content: str):
        if session_id not in self.active_sessions:
            self.active_sessions[session_id] = {'messages': [], 'created_at': datetime.now().isoformat()}
        
        message = {
            'role': role,
            'content': content,
            'timestamp': datetime.now().isoformat()
        }
        
        self.active_sessions[session_id]['messages'].append(message)
        self.active_sessions[session_id]['last_activity'] = datetime.now().isoformat()
        
        # 保存到文件
        try:
            session_file = self.conversations_path / f"{session_id}.json"
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(self.active_sessions[session_id], f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存会话失败: {e}")

session_manager = SimpleSessionManager()

# 优化的响应生成器
class OptimizedResponseGenerator:
    def __init__(self):
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=2)
    
    async def generate_response(self, query: str, session_id: str) -> str:
        """优化的响应生成 - 严格控制在30秒内"""
        start_time = time.time()
        logger.info(f"🧠 开始处理查询: {query}")
        
        try:
            # 1. 快速RAG检索（5秒内）
            rag_results = []
            if rag_retriever:
                try:
                    rag_task = asyncio.create_task(self._rag_search(query))
                    rag_results = await asyncio.wait_for(rag_task, timeout=5.0)
                    logger.info(f"✅ RAG检索完成: {len(rag_results)} 个结果")
                except asyncio.TimeoutError:
                    logger.warning("⚠️ RAG检索超时，跳过")
                except Exception as e:
                    logger.error(f"❌ RAG检索失败: {e}")
            
            # 2. 快速MCP搜索（3秒内）
            mcp_results = []
            if mcp_engine:
                try:
                    mcp_task = asyncio.create_task(self._mcp_search(query))
                    mcp_results = await asyncio.wait_for(mcp_task, timeout=3.0)
                    logger.info(f"✅ MCP搜索完成: {len(mcp_results)} 个结果")
                except asyncio.TimeoutError:
                    logger.warning("⚠️ MCP搜索超时，跳过")
                except Exception as e:
                    logger.error(f"❌ MCP搜索失败: {e}")
            
            # 3. 构建上下文
            context = self._build_context(rag_results, mcp_results)
            
            # 4. DeepSeek生成（严格20秒限制）
            if deepseek_api and deepseek_api.available:
                try:
                    # 使用线程池执行器避免阻塞
                    loop = asyncio.get_event_loop()
                    deepseek_task = loop.run_in_executor(
                        self.executor,
                        self._generate_with_deepseek,
                        query,
                        context
                    )
                    
                    response = await asyncio.wait_for(deepseek_task, timeout=20.0)
                    
                    elapsed_time = time.time() - start_time
                    logger.info(f"✅ 回答生成完成，耗时: {elapsed_time:.2f}秒")
                    
                    if elapsed_time > 30:
                        logger.warning(f"⚠️ 响应时间超过30秒: {elapsed_time:.2f}秒")
                    
                    return response
                    
                except asyncio.TimeoutError:
                    logger.warning("⚠️ DeepSeek生成超时，返回基于检索的回答")
                    return self._fallback_response(query, context)
                except Exception as e:
                    logger.error(f"❌ DeepSeek生成失败: {e}")
                    return self._fallback_response(query, context)
            else:
                logger.warning("⚠️ DeepSeek模型不可用")
                return self._fallback_response(query, context)
                
        except Exception as e:
            logger.error(f"❌ 响应生成异常: {e}")
            return f"抱歉，处理您的问题时出现错误。请稍后再试。错误信息：{str(e)}"
    
    async def _rag_search(self, query: str):
        """异步RAG搜索"""
        if rag_retriever:
            return rag_retriever.search(query, top_k=3)
        return []
    
    async def _mcp_search(self, query: str):
        """异步MCP搜索"""
        if mcp_engine:
            return await mcp_engine.intelligent_search(query, max_results=3)
        return []
    
    def _build_context(self, rag_results, mcp_results):
        """构建上下文"""
        context_parts = []
        
        if mcp_results:
            context_parts.append("【在线检索结果】")
            for i, result in enumerate(mcp_results[:2], 1):
                title = getattr(result, 'title', result.get('title', '未知'))
                content = getattr(result, 'content', result.get('content', ''))[:150]
                context_parts.append(f"{i}. {title}\n   {content}...")
        
        if rag_results:
            context_parts.append("\n【文档检索结果】")
            for i, result in enumerate(rag_results[:2], 1):
                content = result.get('content', '')[:150]
                score = result.get('combined_score', result.get('score', 0))
                context_parts.append(f"{i}. 相关度{score:.2f}: {content}...")
        
        return "\n\n".join(context_parts) if context_parts else ""
    
    def _generate_with_deepseek(self, query: str, context: str):
        """使用DeepSeek生成回答"""
        try:
            return deepseek_api.generate_response(query, context)
        except Exception as e:
            logger.error(f"DeepSeek生成异常: {e}")
            raise
    
    def _fallback_response(self, query: str, context: str):
        """备用回答生成"""
        if context:
            return f"根据检索到的相关资料，针对您的问题「{query}」：\n\n{context[:400]}...\n\n如需更详细的AI分析，请稍后再试。"
        else:
            if "中医" in query or "治疗" in query or "症状" in query:
                return f"您咨询的中医问题「{query}」已收到。建议您详细描述症状，我会为您提供更准确的建议。请注意，任何医疗建议都应咨询专业医生。"
            else:
                return f"您的问题「{query}」已收到。系统正在优化中，请稍后再试或提供更具体的问题描述。"

response_generator = OptimizedResponseGenerator()

# 初始化函数
async def initialize_components():
    """初始化组件"""
    global rag_retriever, deepseek_api, mcp_engine
    
    logger.info("🚀 初始化优化版系统组件...")
    
    # 初始化RAG检索器
    try:
        from intelligent_rag_retriever import IntelligentRAGRetriever
        rag_retriever = IntelligentRAGRetriever()
        if rag_retriever.initialize():
            logger.info(f"✅ RAG检索器初始化成功: {len(rag_retriever.chunks)} 个文档块")
        else:
            logger.error("❌ RAG检索器初始化失败")
    except Exception as e:
        logger.error(f"❌ RAG检索器初始化异常: {e}")
    
    # 初始化DeepSeek API
    try:
        from deepseek_ollama_api import DeepSeekOllamaAPI
        deepseek_api = DeepSeekOllamaAPI()
        if deepseek_api.available:
            logger.info(f"✅ DeepSeek模型可用: {deepseek_api.model_name}")
        else:
            logger.error("❌ DeepSeek模型不可用")
    except Exception as e:
        logger.error(f"❌ DeepSeek API初始化异常: {e}")
    
    # 初始化MCP引擎
    try:
        from intelligent_mcp_service import IntelligentSearchEngine
        mcp_engine = IntelligentSearchEngine()
        logger.info("✅ MCP引擎初始化成功")
    except Exception as e:
        logger.error(f"❌ MCP引擎初始化异常: {e}")
    
    logger.info("🎉 优化版系统初始化完成")

# 启动事件
@app.on_event("startup")
async def startup_event():
    await initialize_components()

# API端点
@app.get("/api/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        components={
            "rag_retriever": rag_retriever is not None,
            "deepseek_api": deepseek_api is not None and deepseek_api.available if deepseek_api else False,
            "mcp_engine": mcp_engine is not None
        },
        vector_db_chunks=len(rag_retriever.chunks) if rag_retriever and hasattr(rag_retriever, 'chunks') else 0,
        service="家庭私人医生小帮手-优化版",
        version="2.1.0-optimized"
    )

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """优化的聊天接口"""
    try:
        query = request.message.strip()
        session_id = request.session_id
        
        if not query:
            raise HTTPException(status_code=400, detail="查询内容不能为空")
        
        if not session_id:
            session_id = session_manager.create_session()
        
        # 添加用户消息
        session_manager.add_message(session_id, 'user', query)
        
        # 生成AI回答
        start_time = time.time()
        response = await response_generator.generate_response(query, session_id)
        response_time = time.time() - start_time
        
        # 添加AI回答
        session_manager.add_message(session_id, 'assistant', response)
        
        return ChatResponse(
            response=response,
            session_id=session_id,
            response_time=response_time,
            timestamp=datetime.now().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"聊天接口错误: {e}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

@app.get("/api/sessions")
async def get_sessions():
    """获取会话列表"""
    try:
        sessions = []
        session_files = list(session_manager.conversations_path.glob("session_*.json"))
        session_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        for file in session_files[:10]:  # 限制10个会话
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                
                messages = session_data.get('messages', [])
                preview = ''
                if messages:
                    last_message = messages[-1]
                    preview = last_message.get('content', '')[:100]
                
                sessions.append({
                    'session_id': file.stem,
                    'created_at': session_data.get('created_at'),
                    'last_activity': session_data.get('last_activity'),
                    'message_count': len(messages),
                    'preview': preview
                })
            except Exception as e:
                logger.warning(f"跳过损坏的会话文件 {file.name}: {e}")
                continue
        
        return {'sessions': sessions}
    except Exception as e:
        logger.error(f"获取会话列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/documents")
async def get_documents():
    """获取文档列表"""
    try:
        documents = []
        
        # 扫描documents目录
        documents_path = Path("./documents")
        if documents_path.exists():
            for file_path in documents_path.glob('*'):
                if file_path.is_file():
                    documents.append({
                        'name': file_path.name,
                        'size': file_path.stat().st_size,
                        'modified': datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                        'type': 'system'
                    })
        
        return {
            'documents': documents,
            'total_count': len(documents),
            'vector_db_chunks': len(rag_retriever.chunks) if rag_retriever and hasattr(rag_retriever, 'chunks') else 0
        }
    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == '__main__':
    logger.info("🚀 启动优化版FastAPI服务器...")
    logger.info("🎯 专注于性能优化，确保响应时间<30秒")
    
    try:
        uvicorn.run(
            "optimized_backend:app",
            host="0.0.0.0",
            port=5001,
            reload=False,
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("🛑 用户中断服务")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        exit(1)
