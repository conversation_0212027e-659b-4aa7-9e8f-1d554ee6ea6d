#!/usr/bin/env python3
"""
测试特定API端点的详细错误信息
"""

import requests
import json
import traceback

def test_sessions_api():
    """测试会话API"""
    print("🔍 测试会话API...")
    
    try:
        response = requests.get('http://localhost:5001/api/sessions', timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   会话数量: {len(data.get('sessions', []))}")
        else:
            print(f"   错误响应: {response.text}")
            
    except Exception as e:
        print(f"   异常: {e}")
        traceback.print_exc()

def test_documents_api():
    """测试文档API"""
    print("\n🔍 测试文档API...")
    
    try:
        response = requests.get('http://localhost:5001/api/documents', timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   文档数量: {len(data.get('documents', []))}")
            print(f"   向量数据库块数: {data.get('vector_db_chunks', 0)}")
        else:
            print(f"   错误响应: {response.text}")
            
    except Exception as e:
        print(f"   异常: {e}")
        traceback.print_exc()

def test_create_session():
    """测试创建会话"""
    print("\n🔍 测试创建会话...")
    
    try:
        response = requests.post('http://localhost:5001/api/sessions', timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   新会话ID: {data.get('session_id')}")
            return data.get('session_id')
        else:
            print(f"   错误响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   异常: {e}")
        traceback.print_exc()
        return None

def test_get_specific_session(session_id):
    """测试获取特定会话"""
    if not session_id:
        return
        
    print(f"\n🔍 测试获取会话 {session_id}...")
    
    try:
        response = requests.get(f'http://localhost:5001/api/sessions/{session_id}', timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   会话数据: {data.get('session_data', {}).keys()}")
        else:
            print(f"   错误响应: {response.text}")
            
    except Exception as e:
        print(f"   异常: {e}")
        traceback.print_exc()

def test_cors_preflight():
    """测试CORS预检请求"""
    print("\n🔍 测试CORS预检请求...")
    
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        response = requests.options('http://localhost:5001/api/chat', headers=headers, timeout=10)
        print(f"   OPTIONS状态码: {response.status_code}")
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        
        print("   CORS响应头:")
        for header, value in cors_headers.items():
            print(f"     {header}: {value}")
            
    except Exception as e:
        print(f"   异常: {e}")
        traceback.print_exc()

def main():
    print("🏥 特定API端点详细测试")
    print("=" * 50)
    
    # 测试各个API
    test_sessions_api()
    test_documents_api()
    
    # 测试会话创建和获取
    session_id = test_create_session()
    test_get_specific_session(session_id)
    
    # 测试CORS
    test_cors_preflight()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main()
