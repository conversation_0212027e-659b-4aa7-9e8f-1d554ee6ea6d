#!/usr/bin/env python3
"""
前端聊天功能深度诊断工具
检查前端界面、JavaScript错误、API连接等问题
"""

import requests
import json
import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

class FrontendChatDiagnostic:
    def __init__(self):
        self.frontend_url = "http://localhost:3000"
        self.backend_url = "http://localhost:5002"
        self.driver = None
        self.results = {}
        
    def setup_browser(self):
        """设置浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")  # 无头模式
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(30)
            return True
        except Exception as e:
            print(f"❌ 浏览器设置失败: {e}")
            print("💡 提示: 请确保已安装Chrome浏览器和ChromeDriver")
            return False
    
    def check_frontend_accessibility(self):
        """检查前端可访问性"""
        print("🔍 检查前端页面可访问性...")
        
        try:
            response = requests.get(self.frontend_url, timeout=10)
            if response.status_code == 200:
                self.results["前端页面访问"] = "✅ 正常"
                
                # 检查页面内容
                content = response.text
                if "chat" in content.lower() and "vue" in content.lower():
                    self.results["页面内容检查"] = "✅ 包含聊天相关元素"
                else:
                    self.results["页面内容检查"] = "⚠️ 页面内容异常"
                    
                return True
            else:
                self.results["前端页面访问"] = f"❌ HTTP {response.status_code}"
                return False
        except Exception as e:
            self.results["前端页面访问"] = f"❌ 连接失败: {e}"
            return False
    
    def check_chat_interface_elements(self):
        """检查聊天界面元素"""
        print("🔍 检查聊天界面元素...")
        
        if not self.driver:
            self.results["聊天界面元素"] = "❌ 浏览器未初始化"
            return False
            
        try:
            self.driver.get(self.frontend_url)
            time.sleep(3)  # 等待页面加载
            
            # 检查页面标题
            title = self.driver.title
            if "家庭私人医生" in title:
                self.results["页面标题"] = "✅ 正确"
            else:
                self.results["页面标题"] = f"⚠️ 异常: {title}"
            
            # 检查聊天输入框
            try:
                input_element = self.driver.find_element(By.CSS_SELECTOR, "textarea[placeholder*='请输入']")
                self.results["聊天输入框"] = "✅ 存在"
            except:
                self.results["聊天输入框"] = "❌ 未找到"
            
            # 检查发送按钮
            try:
                send_button = self.driver.find_element(By.CSS_SELECTOR, "button:contains('发送'), button[class*='btn-success']")
                self.results["发送按钮"] = "✅ 存在"
            except:
                try:
                    send_button = self.driver.find_element(By.XPATH, "//button[contains(text(), '发送')]")
                    self.results["发送按钮"] = "✅ 存在"
                except:
                    self.results["发送按钮"] = "❌ 未找到"
            
            # 检查聊天容器
            try:
                chat_container = self.driver.find_element(By.CSS_SELECTOR, ".chat-container, [class*='chat']")
                self.results["聊天容器"] = "✅ 存在"
            except:
                self.results["聊天容器"] = "❌ 未找到"
            
            return True
            
        except Exception as e:
            self.results["聊天界面元素"] = f"❌ 检查失败: {e}"
            return False
    
    def check_javascript_errors(self):
        """检查JavaScript错误"""
        print("🔍 检查JavaScript控制台错误...")
        
        if not self.driver:
            self.results["JavaScript错误"] = "❌ 浏览器未初始化"
            return False
            
        try:
            # 获取控制台日志
            logs = self.driver.get_log('browser')
            
            errors = []
            warnings = []
            
            for log in logs:
                if log['level'] == 'SEVERE':
                    errors.append(log['message'])
                elif log['level'] == 'WARNING':
                    warnings.append(log['message'])
            
            if errors:
                self.results["JavaScript错误"] = f"❌ 发现 {len(errors)} 个错误"
                for i, error in enumerate(errors[:3]):  # 只显示前3个错误
                    self.results[f"错误{i+1}"] = error[:100] + "..." if len(error) > 100 else error
            else:
                self.results["JavaScript错误"] = "✅ 无严重错误"
            
            if warnings:
                self.results["JavaScript警告"] = f"⚠️ {len(warnings)} 个警告"
            else:
                self.results["JavaScript警告"] = "✅ 无警告"
                
            return len(errors) == 0
            
        except Exception as e:
            self.results["JavaScript错误"] = f"❌ 检查失败: {e}"
            return False
    
    def test_chat_functionality(self):
        """测试聊天功能"""
        print("🔍 测试聊天功能...")
        
        if not self.driver:
            self.results["聊天功能测试"] = "❌ 浏览器未初始化"
            return False
            
        try:
            # 找到输入框并输入测试消息
            input_element = self.driver.find_element(By.CSS_SELECTOR, "textarea")
            test_message = "你好，这是测试消息"
            input_element.clear()
            input_element.send_keys(test_message)
            
            self.results["消息输入"] = "✅ 成功"
            
            # 找到发送按钮并点击
            send_button = self.driver.find_element(By.XPATH, "//button[contains(text(), '发送')]")
            send_button.click()
            
            self.results["发送按钮点击"] = "✅ 成功"
            
            # 等待加载指示器出现
            try:
                WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".typing-indicator, .spinner-border"))
                )
                self.results["加载状态显示"] = "✅ 正常"
            except:
                self.results["加载状态显示"] = "⚠️ 未检测到"
            
            # 等待AI回复（最多30秒）
            try:
                WebDriverWait(self.driver, 30).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".message-assistant"))
                )
                self.results["AI回复显示"] = "✅ 成功"
                
                # 检查回复内容
                reply_elements = self.driver.find_elements(By.CSS_SELECTOR, ".message-assistant")
                if reply_elements:
                    latest_reply = reply_elements[-1].text
                    if len(latest_reply) > 10:
                        self.results["回复内容质量"] = "✅ 正常"
                    else:
                        self.results["回复内容质量"] = "⚠️ 内容过短"
                else:
                    self.results["回复内容质量"] = "❌ 无内容"
                    
            except:
                self.results["AI回复显示"] = "❌ 超时或失败"
            
            return True
            
        except Exception as e:
            self.results["聊天功能测试"] = f"❌ 测试失败: {e}"
            return False
    
    def check_api_network_requests(self):
        """检查API网络请求"""
        print("🔍 检查API网络请求...")
        
        try:
            # 直接测试API
            response = requests.post(
                f"{self.backend_url}/api/chat",
                json={"message": "测试API连接"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                self.results["API直接调用"] = "✅ 成功"
                self.results["API响应时间"] = f"✅ {response.elapsed.total_seconds():.2f}秒"
                
                if "response" in data and len(data["response"]) > 0:
                    self.results["API响应内容"] = "✅ 正常"
                else:
                    self.results["API响应内容"] = "❌ 内容为空"
            else:
                self.results["API直接调用"] = f"❌ HTTP {response.status_code}"
                
        except Exception as e:
            self.results["API直接调用"] = f"❌ 失败: {e}"
    
    def generate_report(self):
        """生成诊断报告"""
        print("\n" + "="*60)
        print("🏥 前端聊天功能诊断报告")
        print("="*60)
        
        # 执行所有检查
        self.check_frontend_accessibility()
        
        if self.setup_browser():
            self.check_chat_interface_elements()
            self.check_javascript_errors()
            self.test_chat_functionality()
        
        self.check_api_network_requests()
        
        # 输出结果
        for check, result in self.results.items():
            print(f"{check:20} : {result}")
        
        # 统计
        total = len(self.results)
        passed = sum(1 for result in self.results.values() if result.startswith("✅"))
        warnings = sum(1 for result in self.results.values() if result.startswith("⚠️"))
        failed = sum(1 for result in self.results.values() if result.startswith("❌"))
        
        print("\n" + "="*60)
        print(f"📊 诊断统计:")
        print(f"   总检查项: {total}")
        print(f"   ✅ 通过: {passed}")
        print(f"   ⚠️ 警告: {warnings}")
        print(f"   ❌ 失败: {failed}")
        
        success_rate = (passed / total) * 100 if total > 0 else 0
        print(f"   🎯 成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("\n🎉 前端聊天功能基本正常！")
        else:
            print("\n⚠️ 前端聊天功能存在问题，需要修复")
        
        return success_rate >= 80
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()

def main():
    """主函数"""
    diagnostic = FrontendChatDiagnostic()
    try:
        success = diagnostic.generate_report()
        return success
    finally:
        diagnostic.cleanup()

if __name__ == "__main__":
    main()
