<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>家庭私人医生小帮手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .chat-container { height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 15px; }
        .message-user { background: #007bff; color: white; padding: 10px; border-radius: 10px; margin: 5px 0; text-align: right; }
        .message-assistant { background: #f8f9fa; padding: 10px; border-radius: 10px; margin: 5px 0; }
        .typing { opacity: 0.7; font-style: italic; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <nav class="navbar navbar-expand-lg navbar-dark bg-success">
            <div class="container">
                <a class="navbar-brand" href="#"><i class="bi bi-heart-pulse me-2"></i>家庭私人医生小帮手</a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="#" onclick="showChat()">💬 聊天</a>
                    <a class="nav-link" href="#" onclick="showSessions()">📋 历史</a>
                    <a class="nav-link" href="#" onclick="showDocuments()">📚 文档</a>
                </div>
            </div>
        </nav>
        
        <div class="container mt-4">
            <!-- 聊天界面 -->
            <div id="chatView" class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-success text-white d-flex justify-content-between">
                            <h5><i class="bi bi-robot me-2"></i>智能医生咨询</h5>
                            <button class="btn btn-outline-light btn-sm" onclick="newChat()">
                                <i class="bi bi-plus-circle me-1"></i>新对话
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="chatContainer" class="chat-container mb-3"></div>
                            <div class="input-group">
                                <input type="text" id="messageInput" class="form-control" placeholder="请输入您的健康问题..." onkeypress="handleKeyPress(event)">
                                <button class="btn btn-success" onclick="sendMessage()">
                                    <i class="bi bi-send-fill"></i> 发送
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 会话历史界面 -->
            <div id="sessionsView" class="row" style="display: none;">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5><i class="bi bi-clock-history me-2"></i>对话历史</h5>
                        </div>
                        <div class="card-body">
                            <div id="sessionsList">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 文档管理界面 -->
            <div id="documentsView" class="row" style="display: none;">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5><i class="bi bi-file-earmark-medical me-2"></i>文档管理</h5>
                        </div>
                        <div class="card-body">
                            <div id="documentsList">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentSessionId = null;
        
        // 显示聊天界面
        function showChat() {
            document.getElementById('chatView').style.display = 'block';
            document.getElementById('sessionsView').style.display = 'none';
            document.getElementById('documentsView').style.display = 'none';
        }
        
        // 显示会话历史
        function showSessions() {
            document.getElementById('chatView').style.display = 'none';
            document.getElementById('sessionsView').style.display = 'block';
            document.getElementById('documentsView').style.display = 'none';
            loadSessions();
        }
        
        // 显示文档管理
        function showDocuments() {
            document.getElementById('chatView').style.display = 'none';
            document.getElementById('sessionsView').style.display = 'none';
            document.getElementById('documentsView').style.display = 'block';
            loadDocuments();
        }
        
        // 新建对话
        function newChat() {
            currentSessionId = null;
            document.getElementById('chatContainer').innerHTML = '<div class="text-center text-muted py-4"><i class="bi bi-chat-heart display-1 mb-3"></i><h4>欢迎使用家庭私人医生小帮手</h4><p>请输入您的健康问题，我会为您提供专业的中医建议。</p></div>';
            document.getElementById('messageInput').focus();
        }
        
        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (!message) return;
            
            input.value = '';
            const container = document.getElementById('chatContainer');
            
            // 添加用户消息
            container.innerHTML += `<div class="message-user">${message}</div>`;
            
            // 添加加载指示器
            container.innerHTML += `<div class="message-assistant typing" id="typing">AI医生正在思考...</div>`;
            container.scrollTop = container.scrollHeight;
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message, session_id: currentSessionId })
                });
                
                const data = await response.json();
                currentSessionId = data.session_id;
                
                // 移除加载指示器
                document.getElementById('typing').remove();
                
                // 添加AI回复
                container.innerHTML += `<div class="message-assistant"><i class="bi bi-robot text-success me-2"></i>${data.response}</div>`;
                container.scrollTop = container.scrollHeight;
                
            } catch (error) {
                document.getElementById('typing').remove();
                container.innerHTML += `<div class="message-assistant text-danger">发送失败: ${error.message}</div>`;
            }
        }
        
        // 处理回车键
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // 加载会话列表
        async function loadSessions() {
            try {
                const response = await fetch('/api/sessions');
                const data = await response.json();
                const container = document.getElementById('sessionsList');
                
                if (data.sessions.length === 0) {
                    container.innerHTML = '<div class="text-center text-muted py-4">暂无对话记录</div>';
                    return;
                }
                
                let html = '<div class="row">';
                data.sessions.forEach(session => {
                    html += `
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">${session.session_id}</h6>
                                    <p class="card-text">${session.preview || '暂无内容'}</p>
                                    <small class="text-muted">${session.message_count} 条消息 | ${new Date(session.created_at).toLocaleString()}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                container.innerHTML = html;
                
            } catch (error) {
                document.getElementById('sessionsList').innerHTML = `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
            }
        }
        
        // 加载文档列表
        async function loadDocuments() {
            try {
                const response = await fetch('/api/documents');
                const data = await response.json();
                const container = document.getElementById('documentsList');
                
                if (data.documents.length === 0) {
                    container.innerHTML = '<div class="text-center text-muted py-4">暂无文档</div>';
                    return;
                }
                
                let html = `<div class="alert alert-info">共 ${data.documents.length} 个文档，${data.vector_db_chunks} 个向量块</div><div class="row">`;
                data.documents.forEach(doc => {
                    html += `
                        <div class="col-md-4 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="bi bi-file-earmark me-2"></i>${doc.name}</h6>
                                    <p class="card-text">大小: ${(doc.size / 1024 / 1024).toFixed(2)} MB</p>
                                    <small class="text-muted">${new Date(doc.modified).toLocaleString()}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                container.innerHTML = html;
                
            } catch (error) {
                document.getElementById('documentsList').innerHTML = `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            newChat();
        });
    </script>
</body>
</html>