#!/usr/bin/env python3
"""
前端UI功能验证脚本
验证用户界面元素和交互功能
"""

import requests
import json
import time

def verify_frontend_ui():
    """验证前端UI功能"""
    print("🔍 验证前端UI功能...")
    
    try:
        # 获取前端页面
        response = requests.get("http://localhost:3000", timeout=10)
        if response.status_code != 200:
            print(f"❌ 前端页面访问失败: {response.status_code}")
            return False
        
        content = response.text
        
        # 检查关键UI元素
        ui_checks = {
            "页面标题": "家庭私人医生小帮手" in content,
            "Vue.js应用容器": 'id="app"' in content,
            "Bootstrap CSS": "bootstrap" in content.lower(),
            "Bootstrap图标": "bootstrap-icons" in content,
            "聊天相关样式": ".chat-container" in content,
            "消息样式": ".message-" in content,
            "输入框样式": "textarea" in content.lower(),
            "按钮样式": "btn" in content,
            "加载动画": "spinner" in content.lower(),
            "响应式设计": "viewport" in content
        }
        
        print("📋 UI元素检查结果:")
        passed = 0
        for check, result in ui_checks.items():
            status = "✅" if result else "❌"
            print(f"   {check}: {status}")
            if result:
                passed += 1
        
        success_rate = (passed / len(ui_checks)) * 100
        print(f"\n🎯 UI完整性: {success_rate:.1f}% ({passed}/{len(ui_checks)})")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ UI验证失败: {e}")
        return False

def test_chat_workflow():
    """测试完整聊天工作流程"""
    print("\n🔄 测试完整聊天工作流程...")
    
    try:
        # 1. 发送测试消息
        print("1️⃣ 发送测试消息...")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:5002/api/chat",
            json={"message": "你好，我想咨询失眠问题"},
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ 消息发送失败: {response.status_code}")
            return False
        
        data = response.json()
        response_time = time.time() - start_time
        session_id = data.get("session_id")
        
        print(f"✅ 消息发送成功 (耗时: {response_time:.2f}秒)")
        print(f"   会话ID: {session_id}")
        print(f"   AI回复长度: {len(data.get('response', ''))} 字符")
        
        # 2. 验证会话保存
        print("\n2️⃣ 验证会话保存...")
        sessions_response = requests.get("http://localhost:5002/api/sessions", timeout=10)
        
        if sessions_response.status_code == 200:
            sessions_data = sessions_response.json()
            sessions = sessions_data.get("sessions", [])
            
            # 查找刚创建的会话
            current_session = None
            for session in sessions:
                if session.get("session_id") == session_id:
                    current_session = session
                    break
            
            if current_session:
                print("✅ 会话保存成功")
                print(f"   会话标题: {current_session.get('title', 'N/A')}")
                print(f"   消息数量: {current_session.get('message_count', 0)}")
            else:
                print("⚠️ 会话未找到，可能保存延迟")
        else:
            print(f"❌ 会话列表获取失败: {sessions_response.status_code}")
        
        # 3. 测试连续对话
        print("\n3️⃣ 测试连续对话...")
        follow_up_response = requests.post(
            "http://localhost:5002/api/chat",
            json={
                "message": "具体有什么中药可以治疗失眠？",
                "session_id": session_id
            },
            timeout=30
        )
        
        if follow_up_response.status_code == 200:
            follow_data = follow_up_response.json()
            if follow_data.get("session_id") == session_id:
                print("✅ 连续对话成功")
                print(f"   回复长度: {len(follow_data.get('response', ''))} 字符")
            else:
                print("⚠️ 会话ID不匹配")
        else:
            print(f"❌ 连续对话失败: {follow_up_response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 聊天工作流程测试失败: {e}")
        return False

def verify_api_endpoints():
    """验证所有API端点"""
    print("\n🔗 验证API端点...")
    
    endpoints = {
        "健康检查": ("GET", "/api/health"),
        "聊天接口": ("POST", "/api/chat"),
        "会话列表": ("GET", "/api/sessions"),
        "文档列表": ("GET", "/api/documents")
    }
    
    base_url = "http://localhost:5002"
    results = {}
    
    for name, (method, path) in endpoints.items():
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{path}", timeout=10)
            elif method == "POST":
                response = requests.post(
                    f"{base_url}{path}",
                    json={"message": "test"},
                    timeout=30
                )
            
            if response.status_code == 200:
                results[name] = "✅ 正常"
            else:
                results[name] = f"⚠️ HTTP {response.status_code}"
                
        except Exception as e:
            results[name] = f"❌ 失败: {str(e)[:50]}"
    
    print("📡 API端点状态:")
    for endpoint, status in results.items():
        print(f"   {endpoint}: {status}")
    
    working_endpoints = sum(1 for status in results.values() if status.startswith("✅"))
    total_endpoints = len(results)
    
    print(f"\n🎯 API可用性: {working_endpoints}/{total_endpoints} 端点正常")
    
    return working_endpoints >= total_endpoints * 0.8

def main():
    """主验证函数"""
    print("🏥 前端聊天功能完整验证")
    print("=" * 50)
    
    # 执行所有验证
    ui_ok = verify_frontend_ui()
    workflow_ok = test_chat_workflow()
    api_ok = verify_api_endpoints()
    
    # 生成最终报告
    print("\n" + "=" * 50)
    print("📊 验证结果汇总:")
    print(f"   前端UI功能: {'✅ 通过' if ui_ok else '❌ 失败'}")
    print(f"   聊天工作流程: {'✅ 通过' if workflow_ok else '❌ 失败'}")
    print(f"   API端点验证: {'✅ 通过' if api_ok else '❌ 失败'}")
    
    overall_success = ui_ok and workflow_ok and api_ok
    
    if overall_success:
        print("\n🎉 前端聊天功能验证完全通过！")
        print("💡 用户现在可以正常使用以下功能:")
        print("   ✅ 在前端界面输入消息")
        print("   ✅ 点击发送按钮发送消息")
        print("   ✅ 接收和查看AI回复")
        print("   ✅ 查看对话历史记录")
        print("   ✅ 进行连续对话")
        print("\n🌐 访问地址: http://localhost:3000")
    else:
        print("\n⚠️ 部分功能存在问题，需要进一步修复")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
