#!/usr/bin/env python3
"""
测试聊天API性能
"""

import requests
import time
import json

def test_chat_performance():
    print('🧪 测试聊天API性能...')
    
    # 测试用例
    test_cases = [
        "我最近感觉疲劳，中医有什么建议？",
        "头痛怎么办？",
        "失眠的中医治疗方法",
        "系统测试"
    ]
    
    results = []
    
    for i, query in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}/{len(test_cases)}: {query}")
        
        start_time = time.time()
        
        try:
            response = requests.post(
                'http://localhost:5002/api/chat',
                json={'message': query},
                timeout=35
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                
                result = {
                    'query': query,
                    'response_time': response_time,
                    'response_length': len(data['response']),
                    'success': True,
                    'preview': data['response'][:150] + '...' if len(data['response']) > 150 else data['response']
                }
                
                print(f'✅ 成功 - 响应时间: {response_time:.2f}秒')
                print(f'📊 回答长度: {result["response_length"]} 字符')
                print(f'📝 回答预览: {result["preview"]}')
                
                if response_time <= 30:
                    print('🎉 性能目标达成：≤ 30秒')
                else:
                    print(f'⚠️ 性能需要优化：{response_time:.2f}秒 > 30秒')
                    
            else:
                result = {
                    'query': query,
                    'response_time': response_time,
                    'success': False,
                    'error': f'HTTP {response.status_code}'
                }
                print(f'❌ 失败 - HTTP {response.status_code}')
                
        except requests.exceptions.Timeout:
            result = {
                'query': query,
                'response_time': 35.0,
                'success': False,
                'error': 'Timeout'
            }
            print('❌ 超时 - 35秒')
            
        except Exception as e:
            result = {
                'query': query,
                'response_time': 0,
                'success': False,
                'error': str(e)
            }
            print(f'❌ 异常 - {e}')
        
        results.append(result)
    
    # 统计结果
    print('\n' + '='*60)
    print('📊 性能测试总结:')
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f'✅ 成功测试: {len(successful_tests)}/{len(results)}')
    print(f'❌ 失败测试: {len(failed_tests)}/{len(results)}')
    
    if successful_tests:
        avg_time = sum(r['response_time'] for r in successful_tests) / len(successful_tests)
        max_time = max(r['response_time'] for r in successful_tests)
        min_time = min(r['response_time'] for r in successful_tests)
        
        print(f'⏱️ 平均响应时间: {avg_time:.2f}秒')
        print(f'⏱️ 最快响应时间: {min_time:.2f}秒')
        print(f'⏱️ 最慢响应时间: {max_time:.2f}秒')
        
        under_30s = len([r for r in successful_tests if r['response_time'] <= 30])
        print(f'🎯 30秒内响应: {under_30s}/{len(successful_tests)} ({under_30s/len(successful_tests)*100:.1f}%)')
        
        if avg_time <= 30:
            print('🎉 性能目标达成：平均响应时间 ≤ 30秒')
        else:
            print(f'⚠️ 性能需要优化：平均响应时间 {avg_time:.2f}秒 > 30秒')
    
    if failed_tests:
        print('\n❌ 失败测试详情:')
        for test in failed_tests:
            print(f'   - {test["query"]}: {test["error"]}')
    
    return results

if __name__ == '__main__':
    test_chat_performance()
