#!/usr/bin/env python3
"""
简化版前端聊天功能诊断
专注于API连接和基本功能验证
"""

import requests
import json
import time
import re

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def check_frontend_page():
    """检查前端页面"""
    log("🔍 检查前端页面...")
    
    try:
        response = requests.get("http://localhost:3000", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # 检查关键元素
            checks = {
                "Vue.js应用": "id=\"app\"" in content,
                "Bootstrap样式": "bootstrap" in content.lower(),
                "聊天相关CSS": "chat" in content.lower(),
                "JavaScript文件": "app.js" in content,
                "图标库": "bootstrap-icons" in content
            }
            
            log("✅ 前端页面访问正常")
            for check, passed in checks.items():
                status = "✅" if passed else "❌"
                log(f"   {check}: {status}")
            
            return True
        else:
            log(f"❌ 前端页面访问失败: HTTP {response.status_code}", "ERROR")
            return False
    except Exception as e:
        log(f"❌ 前端页面连接失败: {e}", "ERROR")
        return False

def check_backend_api():
    """检查后端API"""
    log("🔍 检查后端API...")
    
    try:
        # 健康检查
        response = requests.get("http://localhost:5002/api/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            log("✅ 后端健康检查通过")
            log(f"   状态: {data.get('status')}")
            log(f"   DeepSeek API: {'✅' if data.get('components', {}).get('deepseek_api') else '❌'}")
            log(f"   向量数据库: {data.get('vector_db_chunks', 0)} 个文档块")
            return True
        else:
            log(f"❌ 后端健康检查失败: HTTP {response.status_code}", "ERROR")
            return False
    except Exception as e:
        log(f"❌ 后端API连接失败: {e}", "ERROR")
        return False

def test_chat_api():
    """测试聊天API"""
    log("🔍 测试聊天API...")
    
    try:
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:5002/api/chat",
            json={"message": "你好，这是前端诊断测试"},
            timeout=40
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            log("✅ 聊天API测试成功")
            log(f"   响应时间: {response_time:.2f}秒")
            log(f"   会话ID: {data.get('session_id', 'N/A')}")
            log(f"   回答长度: {len(data.get('response', ''))} 字符")
            log(f"   回答预览: {data.get('response', '')[:100]}...")
            
            # 检查响应格式
            required_fields = ['response', 'session_id', 'timestamp']
            missing_fields = [field for field in required_fields if field not in data]
            
            if missing_fields:
                log(f"⚠️ 响应缺少字段: {', '.join(missing_fields)}", "WARNING")
            else:
                log("✅ 响应格式完整")
            
            return True
        else:
            log(f"❌ 聊天API失败: HTTP {response.status_code}", "ERROR")
            log(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        log(f"❌ 聊天API测试失败: {e}", "ERROR")
        return False

def check_sessions_api():
    """检查会话管理API"""
    log("🔍 检查会话管理API...")
    
    try:
        response = requests.get("http://localhost:5002/api/sessions", timeout=15)
        if response.status_code == 200:
            data = response.json()
            sessions = data.get('sessions', [])
            log("✅ 会话管理API正常")
            log(f"   会话数量: {len(sessions)}")
            
            if sessions:
                # 测试获取单个会话
                session_id = sessions[0].get('session_id')
                if session_id:
                    session_response = requests.get(
                        f"http://localhost:5002/api/sessions/{session_id}",
                        timeout=10
                    )
                    if session_response.status_code == 200:
                        log("✅ 单个会话获取正常")
                    else:
                        log(f"❌ 单个会话获取失败: HTTP {session_response.status_code}", "ERROR")
            
            return True
        else:
            log(f"❌ 会话管理API失败: HTTP {response.status_code}", "ERROR")
            return False
    except Exception as e:
        log(f"❌ 会话管理API测试失败: {e}", "ERROR")
        return False

def check_cors_configuration():
    """检查CORS配置"""
    log("🔍 检查CORS配置...")
    
    try:
        # 模拟前端请求
        headers = {
            'Origin': 'http://localhost:3000',
            'Content-Type': 'application/json'
        }
        
        response = requests.options(
            "http://localhost:5002/api/chat",
            headers=headers,
            timeout=10
        )
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
        }
        
        log("✅ CORS预检请求成功")
        for header, value in cors_headers.items():
            if value:
                log(f"   {header}: {value}")
            else:
                log(f"   {header}: ❌ 未设置")
        
        return True
    except Exception as e:
        log(f"❌ CORS检查失败: {e}", "ERROR")
        return False

def diagnose_potential_issues():
    """诊断潜在问题"""
    log("🔍 诊断潜在问题...")
    
    issues = []
    
    # 检查端口占用
    try:
        import subprocess
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True)
        if ':3000' not in result.stdout:
            issues.append("前端端口3000可能未监听")
        if ':5002' not in result.stdout:
            issues.append("后端端口5002可能未监听")
    except:
        issues.append("无法检查端口状态")
    
    # 检查API配置
    try:
        with open('frontend/src/services/api.js', 'r', encoding='utf-8') as f:
            api_content = f.read()
            if 'localhost:5001' in api_content:
                issues.append("API配置仍指向5001端口，应为5002")
            if 'localhost:5002' not in api_content:
                issues.append("API配置未正确设置为5002端口")
    except:
        issues.append("无法读取API配置文件")
    
    if issues:
        log("⚠️ 发现潜在问题:", "WARNING")
        for issue in issues:
            log(f"   - {issue}")
    else:
        log("✅ 未发现明显问题")
    
    return issues

def main():
    """主诊断函数"""
    log("🏥 开始前端聊天功能诊断")
    log("="*50)
    
    results = {}
    
    # 执行各项检查
    results['前端页面'] = check_frontend_page()
    results['后端API'] = check_backend_api()
    results['聊天功能'] = test_chat_api()
    results['会话管理'] = check_sessions_api()
    results['CORS配置'] = check_cors_configuration()
    
    # 诊断问题
    issues = diagnose_potential_issues()
    
    # 生成报告
    log("="*50)
    log("📊 诊断结果汇总:")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for check, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        log(f"   {check}: {status}")
    
    log(f"\n🎯 总体状态: {passed}/{total} 项通过")
    
    if passed == total and not issues:
        log("🎉 前端聊天功能完全正常！")
        return True
    elif passed >= total * 0.8:
        log("⚠️ 前端聊天功能基本正常，有少量问题需要修复")
        return True
    else:
        log("❌ 前端聊天功能存在严重问题，需要立即修复")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
