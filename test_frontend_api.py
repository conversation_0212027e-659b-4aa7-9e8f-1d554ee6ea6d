#!/usr/bin/env python3
"""
测试前端API连接
模拟前端的API调用来诊断问题
"""

import requests
import json
import time

def test_frontend_api_calls():
    """测试前端可能进行的API调用"""
    print("🔍 测试前端API调用...")
    
    base_url = "http://localhost:5001"
    
    # 测试健康检查
    print("\n1. 测试健康检查 (/api/health)")
    try:
        response = requests.get(f"{base_url}/api/health", timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 测试聊天API
    print("\n2. 测试聊天API (/api/chat)")
    try:
        response = requests.post(f"{base_url}/api/chat", 
                               json={"message": "你好，我想咨询中医问题"}, 
                               timeout=30)
        print(f"   状态码: {response.status_code}")
        data = response.json()
        print(f"   会话ID: {data.get('session_id')}")
        print(f"   响应时间: {data.get('response_time')}秒")
        print(f"   AI回复长度: {len(data.get('response', ''))}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 测试会话API
    print("\n3. 测试会话API (/api/sessions)")
    try:
        response = requests.get(f"{base_url}/api/sessions", timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   会话数量: {len(response.json().get('sessions', []))}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 测试文档API
    print("\n4. 测试文档API (/api/documents)")
    try:
        response = requests.get(f"{base_url}/api/documents", timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   文档数量: {len(response.json().get('documents', []))}")
    except Exception as e:
        print(f"   错误: {e}")

def test_cors_headers():
    """测试CORS头部"""
    print("\n🔍 测试CORS头部...")
    
    try:
        response = requests.options("http://localhost:5001/api/health")
        print(f"   OPTIONS请求状态码: {response.status_code}")
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        
        print("   CORS头部:")
        for header, value in cors_headers.items():
            print(f"     {header}: {value}")
            
    except Exception as e:
        print(f"   CORS测试错误: {e}")

def test_frontend_access():
    """测试前端页面访问"""
    print("\n🔍 测试前端页面访问...")
    
    try:
        response = requests.get("http://localhost:3000", timeout=10)
        print(f"   前端状态码: {response.status_code}")
        
        content = response.text
        
        # 检查关键元素
        checks = {
            'Vue应用容器': 'id="app"' in content,
            'Bootstrap CSS': 'bootstrap' in content.lower(),
            'Vue.js脚本': 'vue' in content.lower() or 'js/app' in content,
            '页面标题': '家庭私人医生小帮手' in content
        }
        
        print("   页面内容检查:")
        for check, passed in checks.items():
            status = "✅" if passed else "❌"
            print(f"     {status} {check}")
            
    except Exception as e:
        print(f"   前端访问错误: {e}")

def main():
    print("🏥 前端API连接测试")
    print("=" * 50)
    
    test_frontend_api_calls()
    test_cors_headers()
    test_frontend_access()
    
    print("\n" + "=" * 50)
    print("💡 如果所有API测试都通过，但前端界面仍然空白，")
    print("   问题可能在于前端JavaScript代码或Vue.js组件。")
    print("   建议检查浏览器开发者工具的控制台错误。")

if __name__ == "__main__":
    main()
